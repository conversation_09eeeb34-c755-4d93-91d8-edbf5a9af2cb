<template>
  <div :class="['chat-header', !isPC && 'chat-header-h5']">
    <!-- <div v-if="!isPC && isNotRoomChat" :class="['chat-header-back', !isPC && 'chat-header-h5-back']" @click="closeChat(currentConversation.conversationID)">
      <Icon :file="backSVG" />
    </div> -->
    <!-- 默认头部 - 当未选中历史选项时显示 -->
    <div class="chat-header-left">
      <div class="chat-header-avatar">
        <img src="@/assets/images/avatar.png" alt="logo" />
      </div>
      <div class="chat-header-container">
        <div v-if="isNotRoomChat" :class="['chat-header-content', !isPC && 'chat-header-h5-content']">神笔马良</div>
        <div></div>
      </div>
    </div>

    <!-- 历史对话头部 - 当选中历史选项时显示 -->
    <!-- <div v-if="isHistoryMode" class="chat-header-history">
      <div class="chat-header-back" @click="exitHistoryMode">
        <img :src="backIcon" alt="back" />
      </div>
      <div class="chat-header-content">
        <div class="message-icon">
          <img :src="messageIcon" alt="" />
        </div>
        <div class="message-text">{{ selectedHistoryName || '神笔马良' }}</div>
      </div>
    </div> -->

    <div class="history-chat" @click="handleHistoryChat">
      <div class="history-chat-icon"></div>
      <div>历史对话</div>
    </div>
    <!-- <div class="chat-header-courseware" @click="openCourseware">
      <div class="chat-header-courseware-icon"></div>
      <div>课件</div>
    </div> -->
    <div class="chat-herder-assistant" @click="handleAssistant" v-if="!chatStore?.specialCourse">
      <div class="chat-herder-assistant-icon" v-if="!chatStore.assistantShow"></div>
      <div class="chat-herder-assistant-icon-active" v-else></div>
      <div>{{ chatStore.assistantShow ? '关闭辅助' : '辅助功能' }}</div>
    </div>
    <div class="chat-header-add" @click="handleAddChat">
      <div class="chat-header-add-icon"></div>
      <div>新建对话</div>
    </div>
    <!-- <div class="chat-header-clear" @click="showConfirmDialog">
      <div class="chat-header-clear-icon"></div>
      <div>清空上下文</div>
    </div> -->
    <!-- <div :class="['chat-header-setting', !isPC && 'chat-header-h5-setting']">
      <div v-for="(item, index) in props.headerExtensionList" :key="index" @click.stop="handleExtensions(item)">
        <Icon :file="item.icon" />
      </div>
    </div> -->
  </div>
  <div class="chat-header" v-if="props.isMultipleSelectMode">
    <div class="chat-header__wrapper">
      <div class="chat-header__cancel" @click="handleCancel">取消</div>
      <div class="chat-header__selected">已选择{{ selectedMessages.length }}条消息</div>
      <div class="chat-header__actions">&nbsp;&nbsp;</div>
    </div>
  </div>
  <Teleport to="body">
    <div class="confirm" v-if="showConfirm" @click="hideConfirm">
      <div class="confirm-content" @click.stop>
        <div class="close-btn" @click="hideConfirm">
          <img :src="closeIcon" alt="" />
        </div>
        <div class="confirm-title">是否清空对话流信息</div>
        <div class="confirm-message">清空后，聊天记录不可恢复，对话内的文件也将被彻底删除</div>
        <div class="confirm-buttons">
          <button class="confirm-btn cancel-btn" @click="hideConfirm">取消</button>
          <button class="confirm-btn confirm-btn-primary" @click="confirmAction">确认</button>
        </div>
      </div>
    </div>
  </Teleport>
</template>
<script lang="ts" setup>
import { ref, onMounted, onUnmounted, withDefaults, inject, computed } from '../../../adapter-vue'
import { TUIStore, StoreName, TUITranslateService, IConversationModel } from '@tencentcloud/chat-uikit-engine'
import { TUIConstants, ExtensionInfo } from '@tencentcloud/tui-core'
import { clearIMHistory, sendCustomMessage } from '@/utils/customIM'
import { clearAllMessage } from '@/api/aiChat'
// import { JoinGroupCard } from '@tencentcloud/call-uikit-vue';
// import Icon from '../../common/Icon.vue'
import backSVG from '../../../assets/icon/back.svg'
import { isPC } from '../../../utils/env'
import TUIChatConfig from '../config'
import { useRoute } from 'vue-router'
import useChatStore from '../../../../../store/modules/chat'
import closeIcon from '@/assets/images/confirm/close.png'
import backIcon from '@/assets/images/chat-header/back.png'
import messageIcon from '@/assets/images/chat-header/message.png'
import { jsbridge } from 'msb-public-library'
import { createGroupSession } from '@/api/chatHistory'

const route = useRoute()
const chatStore = useChatStore()
const selectedMessages = computed(() => chatStore.getSelectedMessages())
const clearMessage = inject<() => void>('clearMessage')

const props = withDefaults(
  defineProps<{
    headerExtensionList: ExtensionInfo[]
    isMultipleSelectMode: boolean
  }>(),
  {
    headerExtensionList: () => [],
    isMultipleSelectMode: false,
  }
)

const emits = defineEmits(['closeChat', 'toggleMultipleSelectMode', 'openHistoryChat', 'exitHistoryMode'])
const currentConversation = ref<IConversationModel>()
const currentConversationName = ref('')
const typingStatus = ref(false)
const groupID = ref('')
const isNotRoomChat = ref<boolean>(TUIChatConfig.getChatType() !== TUIConstants.TUIChat.TYPE.ROOM)
// 确认框相关状态
const showConfirm = ref(false)

// 历史模式相关状态
const isHistoryMode = ref(false)
const selectedHistoryName = ref('')

const isAiChatRoute = computed(() => {
  return route.path === '/ai-chat'
})
const isTeacher = computed(() => {
  return route.query.role === 'teacher'
})
onMounted(() => {
  TUIStore.watch(StoreName.CONV, {
    currentConversation: onCurrentConversationUpdated,
  })

  TUIStore.watch(StoreName.CHAT, {
    typingStatus: onTypingStatusUpdated,
  })
})

onUnmounted(() => {
  TUIStore.unwatch(StoreName.CONV, {
    currentConversation: onCurrentConversationUpdated,
  })

  TUIStore.unwatch(StoreName.CHAT, {
    typingStatus: onTypingStatusUpdated,
  })
})

const closeChat = (conversationID: string | undefined) => {
  emits('closeChat', conversationID)
}

const handleExtensions = (item: ExtensionInfo) => {
  item.listener.onClicked?.({ groupID: groupID.value })
}
const openCourseware = () => {
  const courseId = import.meta.env.VITE_APP_COURSE_ID
  console.log('🚀 ~ openCourseware ~ courseId:', courseId)
  jsbridge?.sendMsg({
    action: 'openPPT',
    params: {
      pptId: courseId, //妙笔测试环境
    },
  })
}
function onCurrentConversationUpdated(conversation: IConversationModel) {
  currentConversation.value = conversation
  groupID.value = currentConversation.value?.groupProfile?.groupID
  currentConversationName.value = currentConversation?.value?.getShowName()
}
function handleAssistant() {
  chatStore.setAssistantShow(!chatStore.assistantShow)
}

// 处理历史对话按钮点击
function handleHistoryChat() {
  emits('openHistoryChat')
}

// 进入历史模式
function enterHistoryMode(historyName?: string) {
  isHistoryMode.value = true
  selectedHistoryName.value = historyName || ''
}

// 退出历史模式
function exitHistoryMode() {
  isHistoryMode.value = false
  selectedHistoryName.value = ''
  emits('exitHistoryMode')
}

// 暴露方法给父组件调用
defineExpose({
  enterHistoryMode,
  exitHistoryMode,
})
function onTypingStatusUpdated(status: boolean) {
  console.log('------', status)
  typingStatus.value = status
  if (typingStatus.value) {
    currentConversationName.value = TUITranslateService.t('TUIChat.对方正在输入')
  } else {
    currentConversationName.value = currentConversation.value?.getShowName() || ''
  }
}

async function clearChat() {
  try {
    // 调用注入的 clearMessage 方法
    if (clearMessage) {
      clearMessage()
    }

    // 发送自定义控制消息
    await sendCustomMessage({
      data: {
        businessID: 'ai_event',
        content: {
          name: 'control',
          data: {},
        },
      },
    })

    // 调用清空消息 API
    if (currentConversation.value?.groupProfile?.groupID) {
      await clearAllMessage({ groupId: currentConversation.value.groupProfile.groupID })
    }
  } catch (error) {
    console.error('清空聊天记录失败:', error)
  }
}

const handleCancel = () => {
  // 取消多选模式
  chatStore.clearSelectedMessages()
  emits('toggleMultipleSelectMode')
}
// 确认框相关方法
const showConfirmDialog = () => {
  showConfirm.value = true
}

const hideConfirm = () => {
  showConfirm.value = false
}

const confirmAction = async () => {
  try {
    clearChat()
    hideConfirm()
  } catch (error) {
    console.error('清空聊天记录失败:', error)
    hideConfirm()
  }
}
const handleAddChat = async () => {
  // 添加新对话
  try {
    const groupID = chatStore?.groupId
    const res: any = await createGroupSession({
      groupId: groupID,
    })
    console.log("🚀 ~ handleAddChat ~ res?.data:", res?.data)
    if (res?.code === 200) {

      // if (clearMessage) {
      //   clearMessage()
      // }
      clearIMHistory()
      chatStore?.setHistoryMessages([])
      chatStore?.setSessionId(res?.data)
      const messageConfig = {
        businessID: 'ai_say',
        content: {
          name: 'sayhello',
          data: {},
        },
      }

      try {
        await sendCustomMessage(
          {
            data: messageConfig,
          }
        )
        console.log(`${route.query?.usertype || route.query?.role}发送${messageConfig.content.name}消息成功`)

      } catch (error) {
        console.error(`发送${messageConfig?.content?.name || '未知'}消息失败:`, error)
      }
    }
  } catch (error) {
    console.log("🚀 ~ handleAddChat ~ error:", error)

  }

}
</script>
<style lang="scss" scoped>
.chat-header {
  width: 100%;
  height: 44px;
  background-color: #f2f5fc;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  &__wrapper {
    height: 100%;
    padding: 0 16px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  &__cancel {
    cursor: pointer;
    transition: opacity 0.2s ease;
    width: 30%;

    font-weight: normal;
    font-size: 17px;
    color: #000000;

    &:hover {
      opacity: 0.8;
    }
  }

  &__selected {
    font-weight: 500;
    width: 40%;

    font-weight: normal;
    font-size: 17px;
    color: #000000;
    text-align: center;
  }

  &__actions {
    width: 30%;
  }
}

.chat-header {
  display: flex;
  min-width: 0;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  height: 60px;

  // margin-top: 25px;
  &-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    min-width: 0;
    margin-right: 20px;
  }

  &-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    overflow: hidden;

    // margin-left: 6px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  &-container {
    display: flex;
    min-width: 0;
    flex-direction: column;
    justify-content: flex-start;
  }

  &-content {
    margin-right: 20px;
    flex: 1;
    font-size: 16px;
    line-height: 30px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #000;
    letter-spacing: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-back,
  &-setting {
    width: 27px;
    height: 27px;

    .icon {
      width: 100%;
      height: 100%;
    }
  }

  .chat-header-history {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-start;
    // background: #d00;

    .chat-header-back {
      width: 12px;
      height: 20px;

      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .chat-header-content {
      display: flex;
      margin-left: 29px;
      height: 100%;
      align-items: center;
      justify-content: flex-start;

      .message-icon {
        width: 35px;
        height: 35px;
        background-size: 100% 100%;
        margin-right: 13px;

        img {
          width: 100%;
          object-fit: contain;
        }
      }

      // 超出省略号
      .message-text {
        height: 16px;
        font-family: HYZhengYuan;
        font-weight: normal;
        font-size: 16px;
        color: #000000;
        line-height: 16px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .history-chat {
    display: flex;
    flex-direction: row;
    margin-right: 25px;
    align-items: center;
    font-size: 16px;
    color: #000000;

    .history-chat-icon {
      width: 23px;
      height: 23px;
      background: url('@/assets/images/history.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 9px;
    }
  }

  .chat-herder-assistant {
    cursor: pointer;
    margin-right: 25px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .chat-herder-assistant-icon {
      width: 23px;
      height: 23px;
      background: url('@/assets/images/assistant.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 9px;
    }

    .chat-herder-assistant-icon-active {
      width: 23px;
      height: 23px;
      background: url('@/assets/images/assistant-active.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 9px;
    }
  }

  .chat-header-courseware {
    cursor: pointer;
    margin-right: 25px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .chat-header-courseware-icon {
      width: 19px;
      height: 19px;
      background: url('@/assets/images/course.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 9px;
    }

    .chat-header-courseware-text {
      font-size: 16px;
      color: #000000;
    }
  }

  .chat-header-clear {
    display: flex;
    flex-direction: row;
    height: 19px;
    width: 106px;
    font-size: 15px;
    align-items: center;
    cursor: pointer;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0.8;
    }

    .chat-header-clear-icon {
      width: 19px;
      height: 19px;
      background: url('@/assets/images/delete-chat.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 8px;
    }
  }

  .chat-header-add {
    display: flex;
    flex-direction: row;
    height: 19px;
    width: 106px;
    font-size: 16px;
    align-items: center;
    cursor: pointer;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0.8;
    }

    .chat-header-add-icon {
      width: 23px;
      height: 23px;
      background: url('@/assets/images/add-session.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 8px;
    }
  }
}

.chat-header-h5 {
  &-back {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-content {
    margin: 0 20px;
    text-align: center;
    font-size: 17px;
    color: #000;
  }
}

/* 确认框样式 */
.confirm {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.45);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.confirm-content {
  width: 350px;
  min-height: 232px;
  background: linear-gradient(to bottom, #c0d3ff 0%, #ffffff 30%);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 20px;
    right: 20px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
    }
  }
}

.confirm-title {
  margin-bottom: 46px;
  text-align: center;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 20px;
  color: #000000;
  line-height: 20px;
}

.confirm-message {
  width: 315px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #313131;
  line-height: 24px;
  text-align: center;
  margin-bottom: 44px;
}

.confirm-buttons {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.confirm-btn {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 23px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.cancel-btn {
    background: #c9cbe3;
    color: #000000;
  }

  &.confirm-btn-primary {
    background: #086df7;
    color: white;

    &:hover {
      background: #086df7;
    }
  }
}
</style>
