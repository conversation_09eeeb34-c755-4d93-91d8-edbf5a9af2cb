<template>
  <div class="custom">
    <template v-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.SERVICE">
      <div>
        <h1>
          <label>{{ extension.title }}</label>
          <a v-if="extension.hyperlinks_text" :href="extension.hyperlinks_text.value" target="view_window">{{
            extension.hyperlinks_text.key }}</a>
        </h1>
        <ul v-if="extension.item && extension.item.length > 0">
          <li v-for="(item, index) in extension.item" :key="index">
            <a v-if="isUrl(item.value)" :href="item.value" target="view_window">{{ item.key }}</a>
            <p v-else>
              {{ item.key }}
            </p>
          </li>
        </ul>
        <article>{{ extension.description }}</article>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.EVALUATE">
      <div class="evaluate">
        <h1>{{ TUITranslateService.t('message.custom.对本次服务评价') }}</h1>
        <ul class="evaluate-list">
          <li v-for="(item, index) in Math.max(customData.score!, 0)" :key="index" class="evaluate-list-item">
            <Icon :file="star" class="file-icon" />
          </li>
        </ul>
        <article>{{ customData.comment }}</article>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.ORDER">
      <div class="order" @click.stop="openLink(customData.link)">
        <img :src="customData.imageUrl" />
        <main>
          <h1>{{ customData.title }}</h1>
          <p>{{ customData.description }}</p>
          <span>{{ customData.price }}</span>
        </main>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.LINK">
      <div class="textLink">
        <p>{{ customData.text }}</p>
        <a :href="customData.link" target="view_window">{{ TUITranslateService.t('message.custom.查看详情>>') }}</a>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.AIMESSAGE">
      <div class="custom-ai-message">
        <div class="custom-ai-message-md-container" v-if="customData.text">
          <div class="custom-ai-message-md" v-html="md.render(customData.text)"></div>
        </div>
        <template v-if="messageStatus !== 'SUCCESS' || !isShowVideo">
          <div class="custom-ai-message-waiting" v-if="isTextWaitingMsg">
            <div class="custom-ai-message-waiting-icon"
              v-if="messageStatus === 'WAIT' || messageStatus === 'RUNNING' || messageStatus === 'QUEUE'">
              <img src="@/assets/images/loading.png" />
            </div>
            <div class="custom-ai-message-waiting-text">
              <p v-if="messageStatus === 'WAIT' || messageStatus === 'RUNNING'">
                <span class="custom-ai-message-waiting-text-progress">{{ progressPercent }}%</span>
                <span class="custom-ai-message-waiting-text-progress-text">正在生成中,请稍等...</span>
              </p>
              <p v-if="messageStatus === 'QUEUE'">正在排队中,请稍等...</p>
              <!-- <p v-if="messageStatus === 'STOP'">你已中止文字生成任务</p> -->
              <div
                v-if="(messageStatus === 'WAIT' || messageStatus === 'QUEUE' || messageStatus === 'RUNNING') && isHiddenBtn"
                class="custom-ai-message-waiting-text-btn" :class="{ 'btn-disabled': isStopTaskLoading }"
                @click.stop="handleStopTask">
                {{ isStopTaskLoading ? '中止中...' : '中止任务' }}
              </div>
            </div>
          </div>
          <div class="custom-ai-message-waiting-img" v-if="isVideoWaitingMsg"
            :style="[currentRatioStyle, { 'background-image': `url(${currentVideoLoadingBg})` }]">
            <div class="custom-ai-message-waiting-img-out">
              <div class="custom-ai-message-waiting-img-container">
                <div class="custom-ai-message-waiting-img-container-box"
                  v-if="messageStatus === 'WAIT' || messageStatus === 'RUNNING' || messageStatus === 'QUEUE' || messageStatus === 'SUCCESS'">
                  <img src="@/assets/images/loading.png" />
                </div>
                <div class="custom-ai-message-waiting-img-container-box-progress"
                  v-if="messageStatus === 'RUNNING' || messageStatus === 'SUCCESS'">{{ progressPercent }}%</div>
                <div class="custom-ai-message-waiting-img-container-box-progress-text"
                  v-if="messageStatus === 'RUNNING' || messageStatus === 'SUCCESS'">小马良正在挥动他的魔法画笔！</div>
                <div v-if="messageStatus === 'STOP'"
                  class="custom-ai-message-waiting-img-container-box-progress-text-stop">你已中止视频生成任务</div>
                <div v-if="messageStatus === 'WAIT' || messageStatus === 'QUEUE'"
                  class="custom-ai-message-waiting-img-container-box-progress-text-queue">正在排队中～</div>
              </div>
              <div class="custom-ai-message-waiting-img-container-box-btn"
                :class="{ 'btn-disabled': isStopTaskLoading }" @click.stop="handleStopTask"
                v-if="(messageStatus === 'WAIT' || messageStatus === 'QUEUE' || messageStatus === 'RUNNING' || messageStatus === 'SUCCESS') && isHiddenBtn">
                {{ isStopTaskLoading ? '中止中...' : '中止任务' }}
              </div>
            </div>
          </div>
        </template>
        <template v-if="isImageWaitingMsg && messageStatus !== 'SUCCESS'">
          <!-- 图片loading - 支持多图片等待状态 -->
          <div class="custom-ai-message-images">
            <div class="custom-ai-message-images-grid">
              <div v-for="(statusItem, index) in processedStatusMsgData" :key="`waiting-${index}`"
                class="custom-ai-message-image-item">
                <div class="custom-ai-message-waiting-img"
                  :style="[getProcessedImageWaitingRatioStyle(statusItem), { 'background-image': `url(${getProcessedImageWaitingBgUrl(statusItem)})` }]">
                  <div class="custom-ai-message-waiting-img-out">
                    <div class="custom-ai-message-waiting-img-container">
                      <div class="custom-ai-message-waiting-img-container-box"
                        v-if="messageStatus === 'WAIT' || messageStatus === 'RUNNING' || messageStatus === 'QUEUE'">
                        <img src="@/assets/images/loading.png" />
                      </div>
                      <div class="custom-ai-message-waiting-img-container-box-progress"
                        v-if="messageStatus === 'RUNNING'">{{ progressPercent }}%</div>
                      <div class="custom-ai-message-waiting-img-container-box-progress-text"
                        v-if="messageStatus === 'RUNNING'">小马良正在挥动他的魔法画笔！</div>
                      <div v-if="messageStatus === 'STOP'"
                        class="custom-ai-message-waiting-img-container-box-progress-text-stop">你已中止图片生成任务</div>
                      <div v-if="messageStatus === 'WAIT' || messageStatus === 'QUEUE'"
                        class="custom-ai-message-waiting-img-container-box-progress-text-queue">正在排队中～</div>
                    </div>
                    <div class="custom-ai-message-waiting-img-container-box-btn"
                      :class="{ 'btn-disabled': isStopTaskLoading }" @click.stop="handleStopTask"
                      v-if="(messageStatus === 'WAIT' || messageStatus === 'QUEUE' || messageStatus === 'RUNNING') && isHiddenBtn && index === 0">
                      {{ isStopTaskLoading ? '中止中...' : '中止任务' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-if="filteredImages.length > 0 && messageItem" class="custom-ai-message-images">
          <div class="custom-ai-message-images-grid">
            <div v-for="(image, index) in filteredImages" :key="index" class="custom-ai-message-image-item"
              :style="getRatioStyleByIndex(index)">
              <IMImagePreview :src="getImageUrlByIndex(customData?.images || [], index)"
                :fullScreenUrl="getFullScreenImageUrl(customData?.images || [], index)" :message="messageItem"
                :isBigImage="isBigImageByIndex(index)" :index="index" @cutouts="handleCutouts" @save="handleSave"
                @quote="handleQuote" />
            </div>
          </div>
        </div>
        <div v-if="customData.function?.name === 'callNativeCamera'">
          <ImageUpload imageSourceType="album" type="btn" />
        </div>
        <div v-if="customData?.video?.url && isShowVideo && messageItem" class="custom-ai-message-video"
          :style="[currentRatioStyle]">
          <VideoPlayer :message="messageItem" :src="customData?.video?.url" :poster="customData?.video?.cover"
            :fluid="true" class="video-player" />
        </div>
        <div v-if="customData?.audio?.url" class="custom-ai-message-audio">
          <!-- <div v-if="isShowAudio"> -->
          <IMAudioPlayer :src="customData?.audio?.url" />
          <!-- </div> -->
          <!-- <div v-else class="audio-skeleton"></div> -->
        </div>
        <div v-if="customData?.questionOption">
          <div class="custom-question-option">
            <div class="custom-question-option-item-box">
              <div v-for="(item, index) in customData?.questionOption?.optionList" :key="index"
                class="custom-question-option-item" @click.stop="handleClick(item)">
                <img v-if="item.optionImgUrl" :src="item.optionImgUrl" alt="图片" />
                <div class="custom-question-option-item-title">
                  <p v-if="item.optionTitle && !item.optionImgUrl">
                    {{ item.optionTitle }}
                  </p>
                </div>
                <span class="no" :class="{
                  selected: selectedOption?.includes(item.optionTitle),
                }">{{ index + 1 }}</span>
              </div>
            </div>
            <div class="submit-btn" :class="{ 'submit-btn-disabled': !isButtonEnabled }" @click.stop="handleSubmit">确定
            </div>
          </div>
        </div>
        <div v-if="customData?.link">
          <div class="custom-link-box">
            <div class="img-box">
              <img :src="ThreeImg" />
            </div>
            <div class="text-box" @click.stop="openLink(customData?.link)">生成三视图</div>
          </div>
        </div>
        <!-- ts-ignore  -->
        <div v-if="customData && customData.searchRespList && customData.searchRespList.length > 0"
          class="custom-search-resp-list">
          <div v-for="(item, index) in customData?.searchRespList" :key="index" class="custom-search-resp-list-item"
            @click.stop="handleSearchResp(item)">
            <div class="img-box">
              <img :src="item.source_url" />
            </div>
            <div class="text-box">
              <div class="img-box"><img :src="item.icon" /></div>
              <div class="text-box-title">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="customData.businessID === CHAT_MSG_CUSTOM_TYPE.AI_CUSTOM_MSG">
      <div class="ai-custom-message" v-if="customData?.content?.name === 'cmd_msg'">
        <p>
          <span class="tool-title">{{ customData?.content?.data?.cmd }}&ensp;</span>{{ customData?.content?.data?.text
          }}
        </p>
        <div
          v-if="Array.isArray(customData?.content?.data?.images) && customData?.content?.data?.images?.length > 0 && messageItem"
          class="ai-custom-message-images">
          <div v-for="(item, index) in customData.content.data.images" :key="index" class="ai-custom-message-image">
            <IMImagePreview :src="getImageUrl(item)" :message="messageItem" :isBigImage="false"
              :fullScreenUrl="getImageUrl(item)" @cutouts="handleCutouts" @save="handleSave" @quote="handleQuote" />
          </div>
        </div>
      </div>
      <div class="ai-custom-message-media"
        v-else-if="messageItem && customData?.content?.data && Array.isArray(customData?.content?.data) && customData?.content?.data[0] && typeof customData?.content?.data[0].width === 'number'"
        :style="{
          width: Math.min(customData.content.data[0].width, 300) + 'px',
          height: (Math.min(customData.content.data[0].width, 300) / customData.content.data[0].width) * customData.content.data[0].height + 'px',
        }">
        <IMImagePreview :message="messageItem" v-if="customData?.content?.name === 'image'"
          :src="customData?.content?.data[0].url" :fullScreenUrl="customData?.content?.data[0].url" :isBigImage="false"
          :index="0" @cutouts="handleCutouts" @save="handleSave" @quote="handleQuote" />
        <VideoPlayer v-else :message="messageItem" :src="customData?.content?.data[0].url"
          :poster="customData?.content?.data[0].cover" :fluid="true" class="video-player" />
      </div>
      <div class="ai-custom-message"
        v-else-if="customData?.content?.name === 'text' && customData?.content?.data && !Array.isArray(customData?.content?.data)">
        <p>{{ (customData.content.data as any)?.text }}</p>
      </div>
    </template>
    <template v-else>
      <span v-html="content.custom" />
    </template>
  </div>
</template>

<script lang="ts" setup>
import { watchEffect, ref, computed, onUnmounted, onMounted } from '../../../../adapter-vue'
import { isUrl, JSONToObject } from '../../../../utils/index'
import { CHAT_MSG_CUSTOM_TYPE } from '../../../../constant'
import { ICustomMessagePayload } from '../../../../interface'
import Icon from '../../../common/Icon.vue'
import star from '../../../../assets/icon/star-light.png'
import * as Image from './message-image.vue'
import * as ImageUpload from '../../../TUIChat/message-input-toolbar/image-upload/index.vue'
// import ImageUpload from "";
import ThreeImg from '@/assets/images/three-img.png'
import DefaultBgVideo from '@/assets/images/default-bg-video.png'
import TUIChatEngine, { TUIConversationService, TUIChatService, TUIGroupService, TUIUserService, TUITranslateService, IMessageModel, TUIStore, StoreName } from '@tencentcloud/chat-uikit-engine'
import useChatStore from '@/store/modules/chat'
import markdownit from 'markdown-it'
import { sendCustomMessageStopTask } from '@/utils/customIM'
import { useRoute } from 'vue-router'
import type { Component } from 'vue'
import VideoPlayer from '@/components/VideoPlayer/index.vue'
import IMImagePreview from '@/components/IMImagePreview/index.vue'
import IMAudioPlayer from '@/components/IMAudioPlayer/index.vue'
import { jsbridge } from 'msb-public-library'
import { addMessageToQuoteListByIndex, saveMediaViaJSBridgeByIndex } from '@/utils/messageUtils'
// 需要先安装highlight.js: npm install highlight.js

const md = markdownit({
  breaks: true,
  html: true,
  linkify: true,
  typographer: true,
})

// Enable tables in markdown-it
md.enable(['table'])

interface Props {
  messageItem?: IMessageModel
  content: any
}

const props = withDefaults(defineProps<Props>(), {
  messageItem: undefined,
  content: undefined,
})

const emits = defineEmits(['openLink'])

const custom = ref()
const message = ref<IMessageModel>()
const extension = ref()
const customData = ref<ICustomMessagePayload>({
  businessID: '',
})
const statusMsgData = ref<any>(null)
const selectedOption = ref<any[]>([])
const isMultiple = ref(false)
const isSingle = ref(false)
const isStopTaskLoading = ref(false)
const chatStore = useChatStore()
const isShowVideo = ref(false)
const isShowAudio = ref(false)
const isAudioPreloading = ref(false)
const isInitialized = ref(false)

type MessageStatus = 'FAIL' | 'SUCCESS' | 'STOP' | 'WAIT' | 'QUEUE' | 'RUNNING'
const messageStatus = ref<MessageStatus | null>(null)

const progressPercent = ref(0)
const progressTimer = ref<number | null>(null)
const startTime = ref<number | null>(null)
const prevMessageStatus = ref<MessageStatus | null>(null)
const completionTimer = ref<number | null>(null)
const isReachingEnd = ref(false)
const currentRatioStyle = computed(() => {
  return chatStore.radioMap[statusMsgData?.value?.ratio] || chatStore.radioMap['16:9']
})
const currentVideoLoadingBg = computed(() => {
  return statusMsgData?.value?.url || DefaultBgVideo
})

// 处理statusMsgData，分为纯image和bigImage的情况
const processedStatusMsgData = computed(() => {
  if (!Array.isArray(customData.value?.statusMsgData)) {
    return []
  }

  // 过滤出image和bigImage类型的状态数据
  return customData.value.statusMsgData.filter((item: any) => {
    return item?.type === 'image' || item?.type === 'bigImage'
  })
})

// 根据处理后的状态数据获取比例样式
const getProcessedImageWaitingRatioStyle = (statusItem: any) => {
  if (!statusItem || !statusItem.ratio) {
    return chatStore.radioMap['16:9']
  }
  return chatStore.radioMap[statusItem.ratio] || chatStore.radioMap['16:9']
}

// 根据处理后的状态数据获取背景图片
const getProcessedImageWaitingBgUrl = (statusItem: any) => {
  if (!statusItem || !statusItem.url) {
    return DefaultBgVideo
  }
  return statusItem.url
}

// 根据索引获取图片等待状态的比例样式（保留原有函数以兼容其他地方的调用）
const getImageWaitingRatioStyle = (index: number = 0) => {
  if (!Array.isArray(customData.value?.statusMsgData) || index < 0 || index >= customData.value.statusMsgData.length) {
    return chatStore.radioMap['16:9']
  }
  const statusData = customData.value.statusMsgData[index]
  return chatStore.radioMap[statusData?.ratio] || chatStore.radioMap['16:9']
}

// 根据索引获取图片等待状态的背景图片（保留原有函数以兼容其他地方的调用）
const getImageWaitingBgUrl = (index: number = 0) => {
  if (!Array.isArray(customData.value?.statusMsgData) || index < 0 || index >= customData.value.statusMsgData.length) {
    return DefaultBgVideo
  }
  const statusData = customData.value.statusMsgData[index]
  return statusData?.url || DefaultBgVideo
}

const isTextWaitingMsg = computed(() => {
  return statusMsgData.value?.type === 'text'
})

const isVideoWaitingMsg = computed(() => {
  return statusMsgData.value?.type === 'video'
})
const isImageWaitingMsg = computed(() => {
  // 兼容原有的单个对象格式
  if (statusMsgData.value?.type === 'image' || statusMsgData.value?.type === 'bigImage') {
    return true
  }

  // 处理新的数组格式
  if (Array.isArray(customData.value?.statusMsgData)) {
    return customData.value.statusMsgData.some((item: any) => item?.type === 'image' || item?.type === 'bigImage')
  }

  return false
})
// 辅助函数：检查指定索引的图片是否为大图
const isBigImageByIndex = (index: number): boolean => {
  // 检查对应的 statusMsgData
  const statusData = getStatusDataByIndex(index)
  if (statusData?.type === 'bigImage') {
    return true
  }

  // 检查对应的 images 中的类型
  if (Array.isArray(customData.value?.images) && index >= 0 && index < customData.value.images.length) {
    const img: any = customData.value.images[index]
    return typeof img === 'object' && img.type === 'LONG_IMAGE'
  }

  return false
}

const isBigImage = computed(() => {
  // 检查第一张图片是否为大图（保持向后兼容）
  return isBigImageByIndex(0)
})

// 过滤图片数组：如果有LONG_IMAGE类型的图片，只取第一个
const filteredImages = computed(() => {
  if (!Array.isArray(customData.value?.images) || customData.value.images.length === 0) {
    return []
  }

  // 查找第一个LONG_IMAGE类型的图片
  const longImageIndex = customData.value.images.findIndex((img: any) => {
    return typeof img === 'object' && img.type === 'LONG_IMAGE'
  })

  // 如果找到LONG_IMAGE类型的图片，只返回第一个
  if (longImageIndex !== -1) {
    return [customData.value.images[longImageIndex]]
  }

  // 如果没有LONG_IMAGE类型的图片，返回所有图片
  return customData.value.images
})
// 预加载音频资源函数
const preloadAudio = (url: string) => {
  if (!url) return
  isAudioPreloading.value = true
  const audio = new Audio()
  audio.preload = 'auto'
  audio.src = url
  audio.load()
}
const isButtonEnabled = computed(() => {
  if (isSingle.value) {
    return selectedOption.value.length === 1
  } else if (isMultiple.value) {
    return selectedOption.value.length >= 2
  }
  return false
})

const isHiddenBtn = computed(() => {
  return !((customData.value as any)?.replyAccount?.includes('teacher') && useRoute().query.role === 'student')
})

const expectTimeInSeconds = computed(() => {
  if (statusMsgData.value?.expectTime) {
    return parseInt(statusMsgData.value.expectTime, 10) || 120
  }
  return 120
})

// 辅助函数：获取图片URL，只支持新的对象格式
const getImageUrl = (imageItem: any): string => {
  if (!imageItem || typeof imageItem !== 'object' || !imageItem.url) {
    return ''
  }
  return imageItem.url
}

// 辅助函数：获取指定索引图片的URL
const getImageUrlByIndex = (images: any[], index: number): string => {
  if (!Array.isArray(images) || index < 0 || index >= images.length) {
    return ''
  }
  return getImageUrl(images[index])
}

// 辅助函数：获取全屏图片URL，优先选择LONG_IMAGE类型
const getFullScreenImageUrl = (images: any[], currentIndex: number = 0): string => {
  console.log('🚀 ~ getFullScreenImageUrl ~ images:', images)
  if (!Array.isArray(images) || images.length === 0) return ''
  // 查找LONG_IMAGE类型的图片
  const longImage = images.filter(img => {
    return typeof img === 'object' && img.type === 'LONG_IMAGE'
  })
  console.log('🚀 ~ getFullScreenImageUrl ~ longImage:', longImage)

  if (longImage?.length > 0) {
    return getImageUrl(longImage[1])
  }
  // 如果指定了当前索引，优先返回当前图片
  if (currentIndex >= 0 && currentIndex < images.length) {
    return getImageUrl(images[currentIndex])
  }
  // 如果没有LONG_IMAGE，返回第一张图片
  return getImageUrl(images[0])
}

// 辅助函数：获取指定索引的状态数据
const getStatusDataByIndex = (index: number) => {
  console.log('🚀 ~ getStatusDataByIndex ~ index:', index)
  console.log('🚀 ~ getStatusDataByIndex ~ customData.value?.statusMsgData:', customData.value?.statusMsgData)
  console.log('🚀 ~ getStatusDataByIndex ~ Array.isArray(customData.value?.statusMsgData):', Array.isArray(customData.value?.statusMsgData))

  if (!Array.isArray(customData.value?.statusMsgData) || index < 0 || index >= customData.value.statusMsgData.length) {
    console.log('🚀 ~ getStatusDataByIndex ~ returning null')
    return null
  }

  const result = customData.value.statusMsgData[index]
  console.log('🚀 ~ getStatusDataByIndex ~ result:', result)
  return result
}

// 辅助函数：获取指定索引的比例样式
const getRatioStyleByIndex = (index: number) => {
  const statusData = getStatusDataByIndex(index)
  console.log('🚀 ~ getRatioStyleByIndex ~ index:', index)
  console.log('🚀 ~ getRatioStyleByIndex ~ statusData:', statusData)
  console.log('🚀 ~ getRatioStyleByIndex ~ statusData?.ratio:', statusData?.ratio)
  console.log('🚀 ~ getRatioStyleByIndex ~ chatStore.radioMap:', chatStore.radioMap)

  const ratio = statusData?.ratio || '16:9'
  const style = chatStore.radioMap[ratio]
  console.log('🚀 ~ getRatioStyleByIndex ~ final ratio:', ratio)
  console.log('🚀 ~ getRatioStyleByIndex ~ final style:', style)

  return style || chatStore.radioMap['16:9']
}

const PROGRESS_STORAGE_KEY = 'chat_progress_data'
const COMPLETED_VIDEOS_KEY = 'chat_completed_videos'
const COMPLETED_AUDIOS_KEY = 'chat_completed_audios'

const getAllProgressData = () => {
  try {
    const allProgressData = localStorage.getItem(PROGRESS_STORAGE_KEY)
    return allProgressData ? JSON.parse(allProgressData) : {}
  } catch (error) {
    console.error('获取进度信息失败', error)
    return {}
  }
}

const getCompletedVideos = () => {
  try {
    const completedVideos = localStorage.getItem(COMPLETED_VIDEOS_KEY)
    return completedVideos ? JSON.parse(completedVideos) : {}
  } catch (error) {
    console.error('获取已完成视频信息失败', error)
    return {}
  }
}

const getCompletedAudios = () => {
  try {
    const completedAudios = localStorage.getItem(COMPLETED_AUDIOS_KEY)
    return completedAudios ? JSON.parse(completedAudios) : {}
  } catch (error) {
    console.error('获取已完成音频信息失败', error)
    return {}
  }
}

const markVideoAsCompleted = (messageId: string) => {
  try {
    const completedVideos = getCompletedVideos()
    completedVideos[messageId] = true
    localStorage.setItem(COMPLETED_VIDEOS_KEY, JSON.stringify(completedVideos))
  } catch (error) {
    console.error('标记已完成视频失败', error)
  }
}

const markAudioAsCompleted = (messageId: string) => {
  try {
    const completedAudios = getCompletedAudios()
    completedAudios[messageId] = true
    localStorage.setItem(COMPLETED_AUDIOS_KEY, JSON.stringify(completedAudios))
  } catch (error) {
    console.error('标记已完成音频失败', error)
  }
}

const isVideoCompleted = (messageId: string) => {
  try {
    const completedVideos = getCompletedVideos()
    return !!completedVideos[messageId]
  } catch (error) {
    console.error('检查视频是否完成失败', error)
    return false
  }
}

const isAudioCompleted = (messageId: string) => {
  try {
    const completedAudios = getCompletedAudios()
    return !!completedAudios[messageId]
  } catch (error) {
    console.error('检查音频是否完成失败', error)
    return false
  }
}

const handleSearchResp = (item: any) => {
  console.log(item.jump_url)
  // Set video information in store for fullscreen display
  // chatStore.setFullscreen(true)
  // chatStore.setVideoSrc(item.jump_url)
  // chatStore.setVideoPoster(item?.source_url)
  // chatStore.setVideoMessage(null)
  console.log(item)
  jsbridge?.sendMsg({
    action: 'openTabUrl',
    params: {
      url: item.jump_url,
    },
  })
}

const saveProgressData = (messageId: string, data: any) => {
  try {
    const allProgressData = getAllProgressData()
    allProgressData[messageId] = data
    localStorage.setItem(PROGRESS_STORAGE_KEY, JSON.stringify(allProgressData))
  } catch (error) {
    console.error('保存进度信息失败', error)
  }
}

const getProgressData = (messageId: string) => {
  try {
    const allProgressData = getAllProgressData()
    return allProgressData[messageId] || null
  } catch (error) {
    console.error('获取单个进度信息失败', error)
    return null
  }
}

const removeProgressData = (messageId: string) => {
  try {
    const allProgressData = getAllProgressData()
    if (allProgressData[messageId]) {
      delete allProgressData[messageId]
      localStorage.setItem(PROGRESS_STORAGE_KEY, JSON.stringify(allProgressData))
    }
  } catch (error) {
    console.error('删除进度信息失败', error)
  }
}

const cleanupOldProgressData = () => {
  try {
    const allProgressData = getAllProgressData()
    const now = Date.now()
    const oneDayMs = 24 * 60 * 60 * 1000

    let hasChanges = false
    for (const messageId in allProgressData) {
      const data = allProgressData[messageId]
      if (data.updatedAt && now - data.updatedAt > oneDayMs) {
        delete allProgressData[messageId]
        hasChanges = true
      }
    }

    if (hasChanges) {
      localStorage.setItem(PROGRESS_STORAGE_KEY, JSON.stringify(allProgressData))
    }
  } catch (error) {
    console.error('清理过期进度信息失败', error)
  }
}

const startProgressTimer = () => {
  cleanupOldProgressData()

  if (isReachingEnd.value) {
    return
  }

  if (message.value?.ID) {
    try {
      const progressData = getProgressData(message.value.ID)

      if (progressData) {
        const savedStartTime = progressData.startTime
        const savedExpectTime = progressData.expectTime

        if (savedExpectTime === expectTimeInSeconds.value) {
          startTime.value = savedStartTime
          if (startTime.value !== null) {
            const elapsedTime = (Date.now() - startTime.value) / 1000
            const totalTime = expectTimeInSeconds.value
            const calculatedPercent = Math.min(Math.floor((elapsedTime / totalTime) * 100), 99)
            progressPercent.value = calculatedPercent
          } else {
            startTime.value = Date.now()
            progressPercent.value = 0
          }
        } else {
          startTime.value = Date.now()
          progressPercent.value = 0
        }
      } else {
        startTime.value = Date.now()
        progressPercent.value = 0
      }
    } catch (error) {
      console.error('恢复进度信息失败', error)
      startTime.value = Date.now()
      progressPercent.value = 0
    }
  } else {
    startTime.value = Date.now()
    progressPercent.value = 0
  }

  if (progressTimer.value) {
    window.clearInterval(progressTimer.value)
  }

  progressTimer.value = window.setInterval(() => {
    if (startTime.value === null) return

    const elapsedTime = (Date.now() - startTime.value) / 1000
    const totalTime = expectTimeInSeconds.value

    const calculatedPercent = Math.min(Math.floor((elapsedTime / totalTime) * 100), 99)

    if (messageStatus.value === 'SUCCESS' && progressPercent.value < 99) {
      completeProgress()
      return
    }

    progressPercent.value = calculatedPercent

    if (message.value?.ID && startTime.value !== null) {
      try {
        const progressData = {
          startTime: startTime.value,
          expectTime: expectTimeInSeconds.value,
          progress: progressPercent.value,
          updatedAt: Date.now(),
        }
        saveProgressData(message.value.ID, progressData)
      } catch (error) {
        console.error('保存进度信息失败', error)
      }
    }

    if (progressPercent.value >= 99) {
      if (progressTimer.value) {
        window.clearInterval(progressTimer.value)
        progressTimer.value = null
      }

      if (messageStatus.value === 'SUCCESS') {
        completeProgress()
      }
    }
  }, 20)
}

const completeProgress = () => {
  if (isReachingEnd.value) {
    return
  }

  isReachingEnd.value = true

  if (completionTimer.value) {
    window.clearTimeout(completionTimer.value)
  }

  const currentProgress = progressPercent.value
  const targetProgress = 100
  const duration = 1500 // 1.5秒
  const steps = 30 // 30个步骤，每步50毫秒
  const stepTime = duration / steps
  const stepSize = (targetProgress - currentProgress) / steps
  let step = 0

  if (progressTimer.value) {
    window.clearInterval(progressTimer.value)
    progressTimer.value = null
  }

  completionTimer.value = window.setInterval(() => {
    step++
    progressPercent.value = Math.floor(Math.min(currentProgress + step * stepSize, 100))

    if (step >= steps || progressPercent.value >= 100) {
      progressPercent.value = 100
      if (completionTimer.value !== null) {
        window.clearInterval(completionTimer.value)
        completionTimer.value = null
      }
      clearProgressData()
      isReachingEnd.value = false
      // 判断消息类型并标记为已完成
      if (message.value?.ID && messageStatus.value === 'SUCCESS') {
        const hasVideoUrl = customData.value?.video?.url
        const isVideoMessage = statusMsgData.value?.type === 'video' || hasVideoUrl

        const hasAudioUrl = customData.value?.audio?.url
        const isAudioMessage = statusMsgData.value?.type === 'audio' || hasAudioUrl

        if (isVideoMessage) {
          markVideoAsCompleted(message.value.ID)
        } else if (isAudioMessage) {
          markAudioAsCompleted(message.value.ID)
        }
      }

      setTimeout(() => {
        isShowVideo.value = true
        isShowAudio.value = true
        isAudioPreloading.value = false
      }, 1500)
    }
  }, stepTime)
}

const clearProgressData = () => {
  if (message.value?.ID) {
    try {
      removeProgressData(message.value.ID)
    } catch (error) {
      console.error('清除进度信息失败', error)
    }
  }
}

const stopAllTimers = () => {
  if (progressTimer.value) {
    window.clearInterval(progressTimer.value)
    progressTimer.value = null
  }

  if (completionTimer.value) {
    window.clearTimeout(completionTimer.value)
    completionTimer.value = null
  }

  isReachingEnd.value = false
  isShowVideo.value = false
  isShowAudio.value = false
  isAudioPreloading.value = false
}

onUnmounted(() => {
  stopAllTimers()
})

watchEffect(() => {
  const currentStatus = messageStatus.value
  const previousStatus = prevMessageStatus.value

  prevMessageStatus.value = currentStatus

  if ((previousStatus === 'WAIT' || previousStatus === 'QUEUE') && currentStatus === 'RUNNING') {
    startProgressTimer()
  }

  if ((previousStatus === 'WAIT' || previousStatus === 'QUEUE') && currentStatus === 'SUCCESS') {
    startProgressTimer()
    completeProgress()
  }

  if (currentStatus === 'SUCCESS' && progressPercent.value > 0) {
    if (progressPercent.value >= 99) {
      completeProgress()
    }
  }

  if (currentStatus === 'FAIL' || currentStatus === 'STOP') {
    stopAllTimers()
    clearProgressData()
  }
})

watchEffect(() => {
  custom.value = props.content
  message.value = props.messageItem

  // 添加安全检查，确保 messageItem 存在
  if (!props.messageItem) {
    return
  }

  const { payload } = props.messageItem
  customData.value = payload.data || ''
  customData.value = JSONToObject(payload.data)
  if (payload.data === CHAT_MSG_CUSTOM_TYPE.SERVICE) {
    extension.value = JSONToObject(payload.extension)
  }
  // 处理数组格式的 statusMsgData，只支持新格式
  if (Array.isArray(customData.value?.statusMsgData) && customData.value.statusMsgData.length > 0) {
    statusMsgData.value = customData.value.statusMsgData[0] // 取第一个元素作为当前状态数据
  } else {
    statusMsgData.value = null
  }
  messageStatus.value = (customData.value?.status as MessageStatus) || null
  isMultiple.value = customData.value?.questionOption?.type?.toUpperCase() === 'CHECKBOX'
  isSingle.value = customData.value?.questionOption?.type?.toUpperCase() === 'RADIO'

  // 检查消息ID
  const messageId = message.value?.ID
  if (!messageId) return

  // 检查视频类型消息
  const hasVideoUrl = customData.value?.video?.url && messageStatus.value === 'SUCCESS'
  const isVideoMessage = statusMsgData.value?.type === 'video' || hasVideoUrl

  // 检查音频类型消息
  const hasAudioUrl = customData.value?.audio?.url && messageStatus.value === 'SUCCESS'
  const isAudioMessage = statusMsgData.value?.type === 'audio' || hasAudioUrl

  // 处理视频消息
  if (isVideoMessage && messageStatus.value === 'SUCCESS') {
    if (isVideoCompleted(messageId)) {
      // 已完成的视频直接显示
      isInitialized.value = true
      isShowVideo.value = true
    } else if (!isInitialized.value) {
      // 首次加载的成功视频走进度条逻辑
      isInitialized.value = true
      isShowVideo.value = false
      startProgressTimer()
      setTimeout(() => {
        completeProgress()
      }, 500)
    }
  } else if (messageStatus.value !== 'SUCCESS') {
    isShowVideo.value = false
  }

  // 处理音频消息
  if (isAudioMessage && messageStatus.value === 'SUCCESS') {
    // 开始预加载音频
    if (customData.value?.audio?.url) {
      preloadAudio(customData.value.audio.url)
    }

    if (isAudioCompleted(messageId)) {
      // 已完成的音频直接显示
      isInitialized.value = true
      isShowAudio.value = true
      isAudioPreloading.value = false
    } else if (!isInitialized.value) {
      // 首次加载的成功音频走进度条逻辑
      isInitialized.value = true
      isShowAudio.value = false
      startProgressTimer()
      setTimeout(() => {
        completeProgress()
      }, 500)
    }
  } else if (messageStatus.value !== 'SUCCESS') {
    isShowAudio.value = false
    isAudioPreloading.value = false
  }
})

const openLink = (url: any) => {
  emits('openLink', url)
}
const handleCutouts = (src: string) => {
  console.log('编辑')
  chatStore.setAiCutoutsShow(true)
  chatStore.setAiCutoutsSrc(src)
}
const handleSave = (index: number = 0) => {
  console.log('保存', props.messageItem, '索引:', index)
  saveMediaViaJSBridgeByIndex(props.messageItem, index)
}
const handleQuote = (index: number = 0) => {
  console.log('引用', '索引:', index)
  debugger
  addMessageToQuoteListByIndex(props.messageItem, index)
}
const handleClick = (item: any) => {
  if (isMultiple.value) {
    const index = selectedOption.value.indexOf(item?.optionTitle)
    if (index === -1) {
      selectedOption.value.push(item?.optionTitle)
    } else {
      selectedOption.value.splice(index, 1)
    }
  }
  if (isSingle.value) {
    selectedOption.value = [item?.optionTitle]
  }
}

const handleSubmit = () => {
  if (!isButtonEnabled.value) {
    return
  }
  console.log('提交选项:', selectedOption.value)
  let promise = TUIChatService.sendTextMessage({
    payload: {
      text: `我选择了:${selectedOption.value.join(',')}`,
    },
  })
  promise.catch(error => { })
}

const handleStopTask = () => {
  if (isStopTaskLoading.value) return
  isStopTaskLoading.value = true
  const replyMsgKey: string | unknown = customData.value?.replyMsgKey
  // 处理数组格式的 statusMsgData，只支持新格式
  // let statusMsgInfo: any = null
  // if (Array.isArray(customData.value?.statusMsgData) && customData.value.statusMsgData.length > 0) {
  //   statusMsgInfo = customData.value.statusMsgData[0] // 取第一个元素
  // }
  const payload = {
    data: {
      businessID: 'ai_say',
      content: {
        name: 'sayStop',
        data: {
          msgKey: replyMsgKey,
          // ...statusMsgInfo,
          text: customData?.value?.text,
        },
      },
    },
  }
  console.log('🚀 ~ handleStopTask ~ payload:', payload)
  sendCustomMessageStopTask(payload).finally(() => {
    isStopTaskLoading.value = false
  })
}

const vHtml = (html: string) => {
  return html
}
</script>
<style lang="scss" scoped>
@import '../../../../assets/styles/common';

.custom {
  font-size: 14px;

  h1 {
    font-size: 14px;
    color: #000;
  }

  h1,
  a,
  p {
    font-size: 14px;
  }

  // Container for the markdown content with horizontal scrolling for tables
  .custom-ai-message-md-container {
    width: 100%;
    max-width: 100%;

    // Style for the markdown content
    :deep(.custom-ai-message-md) {
      width: 100%;

      // Table container with horizontal scrolling
      table {
        border-collapse: collapse;
        width: auto; // Allow table to be sized by content
        margin: 12px 0;
        font-size: 14px;
        display: block;
        overflow-x: auto; // Enable horizontal scrolling
        white-space: nowrap; // Keep table headers and content on one line
        max-width: 100%;
        // max-height: 500px;
      }

      th,
      td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
        word-break: break-word;
        max-width: 500px;
        width: min-content;
        white-space: normal;
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-all;
        -webkit-hyphens: auto;
        -moz-hyphens: auto;
        -ms-hyphens: auto;
        hyphens: auto;
      }

      th {
        background-color: #dce3f3;
        color: #333;
        font-weight: 500;
        position: sticky; // Make headers sticky
        top: 0;
        z-index: 1;
      }

      tr:nth-child(even) {
        background-color: #f2f5fc;
      }

      tr:hover {
        background-color: #f2f5fc;
      }
    }
  }

  .evaluate {
    ul {
      display: flex;
      padding: 10px 0;
    }

    &-list {
      display: flex;
      flex-direction: row;

      &-item {
        padding: 0 2px;
      }
    }
  }

  .order {
    display: flex;

    main {
      padding-left: 5px;

      p {
        font-family: PingFangSC-Regular;
        width: 145px;
        line-height: 17px;
        font-size: 14px;
        color: #999;
        letter-spacing: 0;
        margin-bottom: 6px;
        word-break: break-word;
      }

      span {
        font-family: PingFangSC-Regular;
        line-height: 25px;
        color: #ff7201;
      }
    }

    img {
      width: 67px;
      height: 67px;
    }
  }

  .custom-ai-message-waiting {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding-right: 17px;
    padding-left: 7px;

    .custom-ai-message-waiting-icon {
      width: 20px;
      height: 21px;

      img {
        width: 100%;
        height: 100%;
        animation: rotate 1.5s linear infinite;
      }
    }

    .custom-ai-message-waiting-text {
      color: #000;
      margin-left: 10px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 24px;
      font-style: normal;
      text-transform: none;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;

      .custom-ai-message-waiting-text-progress {
        font-size: 12px;
        color: #000000;
        line-height: 24px;
        font-style: normal;
        text-transform: none;
        margin-right: 8px;
      }

      .custom-ai-message-waiting-text-progress-text {
        font-size: 12px;
        color: #000000;
        line-height: 24px;
        font-style: normal;
        text-transform: none;
      }

      .custom-ai-message-waiting-text-btn {
        margin-left: 17px;
        width: 78px;
        height: 27px;
        background: #2266ff;
        border-radius: 5px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 27px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;

        &:hover:not(.btn-disabled) {
          background: #4785ff;
        }

        &:active:not(.btn-disabled) {
          background: #1a52d9;
        }

        &.btn-disabled {
          background: #cccccc;
          cursor: not-allowed;
        }
      }
    }
  }

  .custom-ai-message-waiting-img {
    position: relative;

    .custom-ai-message-waiting-img-out {
      height: 100%;
      width: 100%;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 10px;

      .custom-ai-message-waiting-img-container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.85);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .custom-ai-message-waiting-img-container-box {
          width: 70px;
          height: 70px;
          background: #ffffff;
          border-radius: 50%;
          opacity: 0.6;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 36px;
            height: 38px;
            display: block;
            animation: rotate 1.5s linear infinite;
          }
        }

        .custom-ai-message-waiting-img-container-box-progress {
          height: 24px;

          font-weight: normal;
          font-size: 19px;
          color: #fff;
          line-height: 24px;
          margin-top: 13px;
        }

        .custom-ai-message-waiting-img-container-box-progress-text {
          width: 171px;
          height: 42px;

          font-weight: normal;
          font-size: 19px;
          color: #ffffff;
          line-height: 24px;
          margin-top: 12px;
          text-align: center;
        }

        .custom-ai-message-waiting-img-container-box-progress-text-stop {
          height: 24px;

          font-weight: normal;
          font-size: 20px;
          color: #ffffff;
          line-height: 24px;
          margin-top: 13px;
        }

        .custom-ai-message-waiting-img-container-box-progress-text-queue {
          height: 24px;

          font-weight: normal;
          font-size: 20px;
          color: #ffffff;
          line-height: 24px;
          margin-top: 13px;
        }
      }

      .custom-ai-message-waiting-img-container-box-btn {
        width: 78px;
        height: 27px;
        background: #2266ff;
        border-radius: 5px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 27px;
        text-align: center;
        position: absolute;
        top: 13px;
        right: 14px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover:not(.btn-disabled) {
          background: #4785ff;
        }

        &:active:not(.btn-disabled) {
          background: #1a52d9;
        }

        &.btn-disabled {
          background: #cccccc;
          cursor: not-allowed;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.custom {
  .custom-ai-message-images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    // margin-top: 8px;
    width: 100%;

    p {
      font-size: 14px;
      color: #d00;
    }

    .custom-ai-message-image {
      position: relative;
      overflow: hidden;
      border-radius: 8px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &.custom-ai-message-image-single {
        width: 240px;
        max-height: 320px;
      }

      &.custom-ai-message-image-half {
        width: 160px;
        height: 160px;
      }
    }
  }

  .custom-ai-message-video {
    // margin-top: 8px;
    overflow: hidden;
    border-radius: 8px;
    background: #ffffff;

    :deep(.video-player-container) {
      width: 100%important;
      height: 100% !important;
    }

    :deep(.video-js) {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
  }

  .custom-search-resp-list {
    display: flex;
    // width: 100%;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    overflow-x: scroll;

    .custom-search-resp-list-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 247px;
      height: 186px;
      background: #909090;
      border-radius: 10px;

      .img-box {
        width: 247px;
        height: 139px;
        background: #C9C9C9;
        border-radius: 10px 10px 0px 0px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .text-box {
        width: 100%;
        height: 47px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        background: #898989;
        border-radius: 0 0 10px 10px;

        .img-box {
          width: 24px;
          height: 24px;
          margin: 0 9px;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .text-box-title {
          width: 140px;
          height: 33px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #fff;
          line-height: 33px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

        }
      }
    }
  }

  .custom-link-box {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    height: 179px;

    .img-box {
      width: 90px;
      height: 103px;

      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .text-box {
      width: 137px;
      height: 34px;
      background: #2266ff;
      border-radius: 17px;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      line-height: 34px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: #4785ff;
      }

      &:active {
        background: #1a52d9;
      }
    }
  }

  .custom-question-option {
    display: flex;
    flex-direction: column;
    align-items: center;

    .custom-question-option-item-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      flex-wrap: wrap;
      margin-top: 10px;

      .custom-question-option-item {
        background: #f2f5fc;
        border-radius: 17px;
        margin-bottom: 10px;
        position: relative;

        .custom-question-option-item-title {
          display: flex;
          flex: 1;

          p {
            font-size: 16px;
            font-weight: normal;
            color: #000;
            margin-left: 59px;
            height: 44px;
            line-height: 44px;
            width: 100%;
            padding: 0 20px;
          }
        }

        img {
          display: block;
          width: 148px;
          height: 148px;
          object-fit: cover;
          border-radius: 17px;
        }

        .no {
          display: inline-block;
          width: 44px;
          height: 21px;
          background: #2266ff;
          border-radius: 11px;
          position: absolute;
          left: 11px;
          top: 13px;
          color: #fff;
          font-size: 12px;
          font-weight: 500;
          line-height: 21px;
          text-align: center;
          transition: background 0.3s ease;

          &.selected {
            background: #eb640c;
          }
        }
      }
    }

    .submit-btn {
      width: 137px;
      height: 34px;
      background: #2266ff;
      border-radius: 17px;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      line-height: 34px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background: #4785ff;
      }

      &:active {
        background: #1a52d9;
      }

      &-disabled {
        background: #cccccc;
        cursor: not-allowed;

        &:hover {
          background: #cccccc;
        }

        &:active {
          background: #cccccc;
        }
      }
    }
  }

  .custom-ai-upload {
    display: inline-block;
    padding: 8px 16px;
    margin-top: 12px;
    background-color: #409eff;
    color: #ffffff;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: #66b1ff;
    }

    &:active {
      background-color: #3a8ee6;
    }
  }

  .custom-ai-message-tips-box {
    display: flex;
    flex-direction: column;

    .custom-ai-message-tips {
      word-break: break-word;

      p {
        background: #dee5f6;
        border-radius: 10px;
        padding: 10px 16px;
        margin-bottom: 10px;
      }
    }
  }

  .ai-custom-message {
    max-width: 340px;
    border-radius: 10px 0 10px 10px;

    p {
      font-size: 14px;
      color: #333;
      line-height: 24px;
      word-break: break-word;

      .tool-title {
        color: #2196f3;
      }
    }

    .ai-custom-message-images {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 14px;
      width: 100%;

      .ai-custom-message-image {
        position: relative;
        overflow: hidden;
        border-radius: 8px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;

          &:hover {
            transform: scale(1.02);
          }
        }

        &:first-child:nth-last-child(1) {
          width: 240px;
          max-height: 320px;
        }

        &:not(:first-child:nth-last-child(1)) {
          width: 48%;
          // height: 160px;
        }
      }
    }
  }

  .ai-custom-message-media {
    max-width: 340px;
    max-height: 340px;
    min-width: 50px;
    min-height: 50px;
    overflow: hidden;
    background-color: #f4f4f4;
    border-radius: 18px;
    border: 2px solid #2266ff;
    box-sizing: content-box;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.custom-ai-message-audio {
  margin-top: 8px;

  .audio-skeleton {
    height: 50px;
    width: 299px;
    background: #ffffff;
    border-radius: 8px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    animation: skeleton-pulse 1.5s infinite;

    &-play {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #e0e0e0;
      margin-right: 12px;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 55%;
        transform: translate(-50%, -50%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 0 8px 12px;
        border-color: transparent transparent transparent #a0a0a0;
      }
    }

    &-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    &-progress {
      height: 6px;
      background: #e0e0e0;
      border-radius: 3px;
      margin-bottom: 8px;
      overflow: hidden;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 30%;
        height: 100%;
        background: #d0d0d0;
        border-radius: 3px;
      }
    }

    &-time {
      width: 60px;
      height: 12px;
      background: #e0e0e0;
      border-radius: 3px;
    }
  }
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    opacity: 0.6;
  }
}

/* 多图片横向网格布局样式 */
.custom-ai-message-images {
  .custom-ai-message-images-grid {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 4px 0;

    /* 滚动条样式 */
    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    /* 移动端滚动优化 */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .custom-ai-message-image-item {
    flex-shrink: 0;
    /* 移除固定的 min-width 和 max-width，让 radioMap 中的样式生效 */
  }
}
</style>
