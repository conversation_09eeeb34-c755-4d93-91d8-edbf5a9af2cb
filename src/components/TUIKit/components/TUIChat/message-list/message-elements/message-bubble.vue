<template>
  <div :class="containerClassNameList" @click="toggleMultipleSelect(!isMultipleSelected)">
    <!-- multiple select radio -->
    <!-- {{ isMessageSelectable }} -->
    <RadioSelect :disabled="!isMessageSelectable" v-if="props.isMultipleSelectMode" class="multiple-select-radio"
      :isSelected="isMultipleSelected" />
    <div :class="{
      'control-reverse': message.flow === 'out',
    }">
      <!-- message-bubble-container -->
      <div class="message-bubble-content">
        <div class="message-bubble-main-content" :class="[message.flow === 'in' ? '' : 'reverse']">
          <Avatar :isAiMessageWithTips="isAiMessageWithTips" useSkeletonAnimation :url="message.avatar || ''"
            :message="message" />
          <main :class="{
            'message-body': true,
            'message-right': message.flow === 'out',
          }">
            <div v-show="!isAiMessageWithTips" v-if="message.flow === 'in' && message.conversationType === 'GROUP'"
              class="message-body-nick-name">
              {{ props.content.showName }}
            </div>
            <div v-if="!isAiMessageWithTips"
              :class="['message-body-main', message.flow === 'out' && 'message-body-main-reverse']">
              <div :class="[
                'blink',
                'message-body-content',
                message.flow === 'out' ? 'content-out' : isAiMessageWithTips ? '' : 'content-in',
                message.hasRiskContent && 'content-has-risk',
                isNoPadding || isCustomNoPadding ? 'content-no-padding' : '',
                isNoPadding && isBlink ? 'blink-shadow' : '',
                !isNoPadding && isBlink ? 'blink-content' : '',
              ]">
                <div class="content-main">
                  <img
                    v-if="(message.type === TYPES.MSG_IMAGE || message.type === TYPES.MSG_VIDEO) && message.hasRiskContent"
                    :class="['message-risk-replace', !isPC && 'message-risk-replace-h5']" :src="riskImageReplaceUrl" />
                  <template v-else>
                    <slot name="messageElement" />
                    <slot name="TUIEmojiPlugin" />
                  </template>
                </div>
                <!-- Risk Content Tips -->
                <div v-if="message.hasRiskContent" class="content-has-risk-tips">
                  {{ riskContentText }}
                </div>
              </div>
              <!-- audio unplay mark -->
              <div v-if="isDisplayUnplayMark" class="audio-unplay-mark" />
              <!-- Send Fail Icon -->
              <div v-if="message.status === 'fail' || message.hasRiskContent" class="message-label fail"
                @click="resendMessage()">!
              </div>
              <!-- Loading Icon -->
              <Icon v-if="message.status === 'unSend' && needLoadingIconMessageType.includes(message.type)"
                class="message-label loading-circle" :file="loadingIcon" :width="'15px'" :height="'15px'" />
              <!-- Read & Unread -->
              <ReadStatus class="message-label align-self-bottom" :message="shallowCopyMessage(message)"
                @openReadUserPanel="openReadUserPanel" />
            </div>

            <div class="message-bubble-tips" :class="{ 'message-bubble-tips-top': isWelcomeMessageWithTips }"
              v-if="getLastTipsMessageID() === message.ID">
              <div v-for="item in welcomeMessageWithTipsTextList" :key="item" class="message-bubble-tips-item">
                <div class="message-bubble-tips-item-text" @click="handleClickTip(item)">{{ item }}</div>
              </div>
            </div>
            <div class="message-bubble-tool"
              v-if="getLastValidMessageID() === message.ID && (messageStatus === 'SUCCESS' || isAiCustomMessage)">
              <!-- 判断是否纯文本 -->
              <div @click="handleCopy(message)"
                v-if="message.type === 'TIMTextElem' || (message?.payload?.data && JSON.parse(message?.payload?.data)?.text)">
                <img src="@/assets/images/bubble/copy.png" alt="" />
              </div>

              <div @click="handleQuote(message)" v-if="!isNotQuote">
                <img src="@/assets/images/bubble/quote.png" alt="" />
              </div>
              <div @click="handleRespin(message)" v-if="isAgentMessage">
                <img src="@/assets/images/bubble/respin.png" alt="" />
                <span>重新生成</span>
              </div>
              <!-- <span @click="handlePlay(message)">播放</span> -->
            </div>
            <div v-if="referenceList?.length >= 5" class="reference-list-more">
              <p>对话流记录</p>
              <div v-for="item in referenceList.slice(0, 4)" :key="item.id">{{ item?.userName }}:&nbsp;&nbsp;{{
                typesMap[item.type]
              }}</div>
            </div>
            <div class="reference-list" v-if="referenceList?.length < 5">
              <div v-for="item in referenceList" :key="item.id">
                <div v-if="item.type === 'text'" class="reference-text">{{ item?.userName }}:&nbsp;&nbsp;{{ item.data }}
                </div>
                <div v-if="item.type === 'image'" class="reference-image">
                  <div class="reference-image-title" v-if='item?.userName'>{{ item?.userName }}:</div>
                  <img :src="item.data" alt="" />
                </div>

                <div v-if="item.type === 'video'" class="reference-video">
                  <div v-if='item?.userName' class="reference-video-title">{{ item?.userName }}:</div>
                  <!-- <video :src="item.data" controls></video> -->
                  <div class="img-box">
                    <img :src="item.cover" alt="" class="cover" />
                    <img :src="playIcon" alt="" class="play" />
                  </div>
                </div>
                <div v-if="item.type === 'audio'" class="reference-audio">
                  <div v-if='item?.userName' class="reference-audio-title">{{ item?.userName }}:</div>
                  <div class="img-box">
                    <img :src="audioChatImg" alt="" class="cover" />
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
        <!-- message extra area -->
        <div class="message-bubble-extra-content">
          <!-- extra: message translation -->
          <MessageTranslate :class="message.flow === 'out' ? 'reverse' : 'flex-row'" :message="message" />
          <!-- extra: message convert voice to text -->
          <MessageConvert :class="message.flow === 'out' ? 'reverse' : 'flex-row'" :message="message" />
          <!-- extra: message quote -->
          <MessageQuote :class="message.flow === 'out' ? 'reverse' : 'flex-row'" :message="message"
            @blinkMessage="(messageID: string) => blinkMessage(messageID)" @scrollTo="scrollTo" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, toRef, ref, watchEffect, watch, toRefs } from '../../../../adapter-vue'
import TUIChatEngine, { TUITranslateService, IMessageModel, StoreName, TUIStore, TUIChatService } from '@tencentcloud/chat-uikit-engine'
import Icon from '../../../common/Icon.vue'
import ReadStatus from './read-status/index.vue'
import MessageQuote from './message-quote/index.vue'
import Avatar from '../../../common/Avatar/index.vue'
import MessageTranslate from './message-translate/index.vue'
import MessageConvert from './message-convert/index.vue'
import RadioSelect from '../../../common/RadioSelect/index.vue'
import loadingIcon from '../../../../assets/icon/loading.png'
import { ElMessage } from 'element-plus'
import { shallowCopyMessage } from '../../utils/utils'
import { isPC } from '../../../../utils/env'
import useChatStore from '@/store/modules/chat'
import CopyManager from '../../utils/copy'
import playIcon from '@/assets/images/play.png'
import { addMessageToQuoteList } from '@/utils/messageUtils'
import audioChatImg from '@/assets/images/audio-chat.png'
import { sendCustomMessage } from '@/utils/customIM'
interface IProps {
  messageItem: IMessageModel
  content?: any
  classNameList?: string[]
  blinkMessageIDList?: string[]
  isMultipleSelectMode?: boolean
  isAudioPlayed?: boolean | undefined
  multipleSelectedMessageIDList?: string[]
  isMessageSelectable?: boolean
}

interface IEmits {
  (e: 'resendMessage'): void
  (e: 'blinkMessage', messageID: string): void
  (e: 'setReadReceiptPanelVisible', visible: boolean, message?: IMessageModel): void
  (e: 'changeSelectMessageIDList', options: { type: 'add' | 'remove' | 'clearAll'; messageID: string }): void
  // Only for uniapp
  (e: 'scrollTo', scrollHeight: number): void
}
type MessageStatus = 'FAIL' | 'SUCCESS' | 'STOP' | 'WAIT' | 'QUEUE' | 'RUNNING'
const messageStatus = ref<MessageStatus | null>(null)

const emits = defineEmits<IEmits>()
const chatStore = useChatStore()
const props = withDefaults(defineProps<IProps>(), {
  messageItem: () => ({} as IMessageModel),
  content: () => ({}),
  isAudioPlayed: false,
  blinkMessageIDList: () => [],
  classNameList: () => [],
  isMultipleSelectMode: false,
  multipleSelectedMessageIDList: () => [],
  isMessageSelectable: true,
})
const typesMap: Record<string, string> = {
  text: '[文本]',
  image: '[图片]',
  video: '[视频]',
  audio: '[音频]',
}
const TYPES = TUIChatEngine.TYPES
const riskImageReplaceUrl = 'https://web.sdk.qcloud.com/component/TUIKit/assets/has_risk_default.png'
const needLoadingIconMessageType = [TYPES.MSG_LOCATION, TYPES.MSG_TEXT, TYPES.MSG_CUSTOM, TYPES.MSG_MERGER, TYPES.MSG_FACE]
const { blinkMessageIDList, messageItem: message } = toRefs(props)
const isAiCustomMessage = computed(() => {
  const payload = props.messageItem?.payload
  if (payload?.data) {
    const customData = JSON.parse(payload.data)
    return customData?.businessID === 'ai_custom_msg'
  }
  return false
})
const isAgentMessage = computed(() => {
  const from = props.messageItem?.from
  // 
  const customData = JSON.parse(props.messageItem?.payload?.data)

  const courseList = customData?.courseList

  return from.indexOf('agent') > -1 && !courseList
})
const isNotQuote = computed(() => {
  const payload = props.messageItem?.payload

  if (payload?.data) {
    const customData = JSON.parse(payload.data)
    if (customData?.searchRespList?.length > 0 || customData?.tips?.length > 0 || customData?.images?.length > 1) {
      return true
    } else {
      return false
    }
  }
  return false
})
const isMultipleSelected = computed<boolean>(() => {
  return props.multipleSelectedMessageIDList.includes(message.value.ID)
})
watchEffect(() => {
  const payload = props.messageItem?.payload
  const customData = JSON.parse(payload?.data || '{}')
  messageStatus.value = (customData?.status as MessageStatus) || null
})

// 添加视频状态相关的变量
const isShowVideo = ref(false)
const isInitialized = ref(false)

// 添加检查视频完成状态的函数
const isVideoCompleted = (messageId: string) => {
  const completedVideos = localStorage.getItem('completedVideos')
  if (completedVideos) {
    return JSON.parse(completedVideos).includes(messageId)
  }
  return false
}

// 监听消息状态变化
watch(
  () => props.messageItem,
  message => {
    if (!message) return

    // 检查是否是已完成视频的消息
    const messageId = message?.ID
    try {
      const payload = JSON.parse(message.payload?.data || '{}')
      // 避免直接使用isVideoWaitingMsg.value，改为直接检查条件
      const hasVideoUrl = payload?.video?.url && message.status === 'SUCCESS'
      const isVideoMessage = payload?.video

      if (messageId && isVideoMessage && message.status === 'SUCCESS' && isVideoCompleted(messageId)) {
        // 如果是已完成的视频，直接显示
        isInitialized.value = true
        isShowVideo.value = true
      } else if (!isInitialized.value && message.status === 'SUCCESS') {
        // 对于首次加载的成功视频，走原来的进度条逻辑
        isInitialized.value = true
        isShowVideo.value = false
        // 这里可以添加进度条相关逻辑如果需要
        setTimeout(() => {
          isShowVideo.value = true
        }, 500)
      } else if (message.status !== 'SUCCESS') {
        isShowVideo.value = false
      }
    } catch (error) {

      isShowVideo.value = false
    }
  },
  { immediate: true }
)

// 获取最后一个非tips、非stop且非delete的消息ID
const getLastValidMessageID = () => {
  const currentMessageList = TUIStore.getData(StoreName.CHAT, 'messageList') || []
  if (!currentMessageList.length) return ''

  // 从后向前遍历消息列表
  for (let i = currentMessageList.length - 1; i >= 0; i--) {
    const message = currentMessageList[i]

    // 跳过已删除的消息
    if (message.isDeleted) continue

    // 检查消息的payload.data
    if (message.payload?.data) {
      try {
        const data = JSON.parse(message.payload.data)

        // 跳过ai_event类型的消息
        if (data.businessID === 'ai_event') continue

        // 跳过ai_stop类型的消息
        if (data.businessID === 'ai_stop') continue

        // 跳过ai_signal类型的消息
        if (data.businessID === 'ai_signal') continue

        // 跳过hidden_message类型的消息
        if (data.businessID === 'hidden_message') continue

        // 跳过ai_say类型的消息
        if (data.businessID === 'ai_say') continue

        // 跳过say_hidden_message类型的消息
        if (data.businessID === 'say_hidden_message') continue

        // 跳过tips类型的消息
        if (data.businessID === 'ai_message' && Array.isArray(data?.tips) && data.tips.length > 0 && !data?.text?.length) continue
      } catch (error) {

      }
    }

    // 返回符合条件的消息ID
    return message.ID
  }

  return ''
}

// 获取最后一条tips消息的ID
const getLastTipsMessageID = () => {
  const currentMessageList = [...(chatStore?.historyMessages || []), ...(TUIStore.getData(StoreName.CHAT, 'messageList') || [])]
  if (!currentMessageList.length) return ''

  let lastTipsID = ''
  let foundTips = false

  // 从后向前遍历消息列表
  for (let i = currentMessageList.length - 1; i >= 0; i--) {
    const message = currentMessageList[i]

    // 跳过已删除的消息
    if (message.isDeleted) continue

    // 检查消息的payload.data
    if (message.payload?.data) {
      try {
        const data = JSON.parse(message.payload.data)

        if (!foundTips) {
          // 检查当前消息是否为允许的自定义消息类型
          const isAllowedCustomMessage =
            data.businessID === 'ai_event' || data.businessID === 'ai_stop' || data.businessID === 'ai_signal' || (data.businessID === 'ai_event' && data.content?.name === 'delete')

          if (!isAllowedCustomMessage) {
            // 检查是否为tips消息
            if (data.businessID === 'ai_message' && Array.isArray(data?.tips) && data.tips.length > 0) {
              lastTipsID = message.ID
              foundTips = true
            }
            // 如果既不是允许的自定义消息也不是tips，直接返回空
            else {
              return ''
            }
          }
        } else {
          // 已找到tips消息，确保之后的消息都是允许的类型
          return lastTipsID
        }
      } catch (error) {

        continue
      }
    }
  }

  return lastTipsID
}

// 将方法暴露给父组件
defineExpose({
  getLastValidMessageID,
  getLastTipsMessageID, // 添加新方法到暴露列表
})

const handleClickTip = async (tip: string) => {
  try {
    await sendCustomMessage({
      data: {
        businessID: 'ai_custom_msg',
        content: {
          name: 'cmd_msg',
          data: {
            text: tip,
          },
        },
      },
    })
    // chatStore.setSendStatus(true)
  } catch (error) {

  }
}
const isDisplayUnplayMark = computed<boolean>(() => {
  return message.value.flow === 'in' && message.value.status === 'success' && message.value.type === TYPES.MSG_AUDIO && !props.isAudioPlayed
})

const containerClassNameList = computed(() => {
  return ['message-bubble', isMultipleSelected.value ? 'multiple-selected' : '', ...props.classNameList]
})

// When an emoji is deleted, the `reactionList` will update the corresponding emoji's `totalUserCount`.
const hasEmojiReaction = computed(() => {
  return message.value?.reactionList?.some((item: any) => item.totalUserCount !== 0)
})

const isNoPadding = computed(() => {
  return !hasEmojiReaction.value && [TYPES.MSG_IMAGE, TYPES.MSG_VIDEO, TYPES.MSG_MERGER].includes(message.value.type)
})
const isCustomNoPadding = computed(() => {
  try {
    const data = JSON.parse(message.value.payload.data)
    return !hasEmojiReaction.value && [TYPES.MSG_CUSTOM].includes(message.value.type) && ['video', 'image'].includes(data?.content?.name)
  } catch (error) {
    return false
  }
})
const referenceList = computed(() => {
  const message = props.messageItem

  if (message.type === 'TIMCustomElem') {
    const payloadData = JSON.parse(message.payload?.data)

    return payloadData?.content?.data?.referenceList
  }
  return []
})
const riskContentText = computed<string>(() => {
  let content = TUITranslateService.t('TUIChat.涉及敏感内容') + ', '
  if (message.value.flow === 'out') {
    content += TUITranslateService.t('TUIChat.发送失败')
  } else {
    content += TUITranslateService.t(message.value.type === TYPES.MSG_AUDIO ? 'TUIChat.无法收听' : 'TUIChat.无法查看')
  }
  return content
})

const isBlink = computed(() => {
  if (message.value?.ID) {
    return blinkMessageIDList?.value?.includes(message.value.ID)
  }
  return false
})

const isAiMessageWithTips = computed(() => {
  try {
    if (message.value?.payload?.data) {
      const payloadData = JSON.parse(message.value.payload.data)
      return payloadData.businessID === 'ai_message' && Array.isArray(payloadData?.tips) && payloadData.tips.length > 0 && !payloadData?.text?.length
    }
    return false
  } catch (error) {
    return false
  }
})
const welcomeMessageWithTipsTextList = computed(() => {
  try {
    if (message.value?.payload?.data) {
      const payloadData = JSON.parse(message.value.payload.data)
      if (payloadData.businessID === 'ai_message' && Array.isArray(payloadData?.tips) && payloadData.tips.length > 0) {
        return payloadData.tips
      }
    }
    return []
  } catch (error) {

    return []
  }
})
const isWelcomeMessageWithTips = computed(() => {
  try {
    if (message.value?.payload?.data) {
      const payloadData = JSON.parse(message.value.payload.data)
      return payloadData.businessID === 'ai_message' && Array.isArray(payloadData?.tips) && payloadData.tips.length > 0 && payloadData?.text?.length
    }
    return false
  } catch (error) {
    return false
  }
})
function toggleMultipleSelect(isSelected: boolean) {
  if (!props.isMultipleSelectMode) {
    return
  }
  emits('changeSelectMessageIDList', {
    type: isSelected ? 'add' : 'remove',
    messageID: message.value.ID,
  })
}

function resendMessage() {
  if (!message.value?.hasRiskContent) {
    emits('resendMessage')
  }
}

function blinkMessage(messageID: string) {
  emits('blinkMessage', messageID)
}

function scrollTo(scrollHeight: number) {
  emits('scrollTo', scrollHeight)
}

function openReadUserPanel() {
  emits('setReadReceiptPanelVisible', true, message.value)
}
const handleQuote = (message: IMessageModel) => {

  addMessageToQuoteList(message)
}
const handleCopy = (message: IMessageModel) => {

  // CopyManager.copySelection(message?.payload?.text || '')

  if (message?.payload?.text) {
    CopyManager.copySelection(message?.payload?.text)
    // ElMessage.success('复制成功!')
  } else {
    if (message?.payload.data) {
      const data = JSON.parse(message?.payload.data)
      CopyManager.copySelection(data?.text || data?.content?.data?.text)
      // ElMessage.success('复制成功!')
    }
  }
}
const handleRespin = (message: IMessageModel) => {

  // alert('重抽')
  const payloadData: any = props.messageItem?.payload
  const customData = JSON.parse(payloadData.data)
  const customMsg = customData.customMsg
  const payload = {
    data: {
      businessID: 'say_hidden_message',
      content: customMsg,
    },
  }

  sendCustomMessage(payload).finally(() => {

  })
}
const handlePlay = (message: IMessageModel) => {

}
</script>

<style lang="scss" scoped>
* {
  display: flex;
  flex-direction: column;
  min-width: 0;
  box-sizing: border-box;
}

.message-bubble {
  padding: 10px 15px;
  display: flex;
  flex-direction: row;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  &.multiple-selected {
    background-color: #f0f0f0;
  }

  .multiple-select-radio {
    margin-right: 12px;
    flex: 0 0 auto;
  }

  .control-reverse {
    flex: 1 1 auto;
    flex-direction: row-reverse;
  }

  .message-bubble-main-content {
    display: flex;
    flex-direction: row;

    .message-avatar {
      display: block;
      width: 36px;
      height: 36px;
      border-radius: 5px;
      flex: 0 0 auto;
    }

    .message-right {
      align-items: flex-end !important;
    }

    .message-body {
      display: flex;
      flex: 0 1 auto;
      flex-direction: column;
      align-items: flex-start;
      margin: 0 8px;

      .message-body-nick-name {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
        color: #999;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .message-body-main {
        max-width: 100%;
        display: flex;
        flex-direction: row;
        min-width: 0;
        box-sizing: border-box;

        &-reverse {
          flex-direction: row-reverse;
        }

        .audio-unplay-mark {
          flex: 0 0 auto;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #f00;
          margin: 5px;
        }

        .message-body-content {
          display: flex;
          flex-direction: column;
          min-width: 0;
          box-sizing: border-box;
          padding: 12px;
          font-size: 14px;
          color: #000;
          letter-spacing: 0;
          word-wrap: break-word;
          word-break: break-all;
          position: relative;

          .content-main {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            align-content: flex-start;
            border: 0 solid black;
            margin: 0;
            padding: 0;
            min-width: 0;

            .message-risk-replace {
              width: 130px;
              height: 130px;
            }

            :deep() {
              .custom-ai-message-md {
                p {
                  margin: 0;
                  padding: 0;
                  line-height: 24px;
                }

                code {
                  white-space: pre-wrap;
                }

                img {
                  width: auto;
                  max-height: 300px;
                }
              }
            }
          }

          .content-has-risk-tips {
            font-size: 12px;
            color: #fa5151;
            font-family: PingFangSC-Regular;
            margin-top: 5px;
            border-top: 1px solid #e5c7c7;
            padding-top: 5px;
          }
        }

        .content-in {
          background: #ffffff;
          border-radius: 0 10px 10px;
        }

        .content-out {
          background: #dceafd;
          border-radius: 10px 0 10px 10px;
        }

        .content-no-padding {
          padding: 0;
          background: transparent;
          border-radius: 10px;
          overflow: hidden;
        }

        .content-no-padding.content-has-risk {
          padding: 12px;
        }

        .content-has-risk {
          background: rgba(250, 81, 81, 0.16);
        }

        .blink-shadow {
          @keyframes shadow-blink {
            50% {
              box-shadow: rgba(255, 156, 25, 1) 0 0 10px 0;
            }
          }

          box-shadow: rgba(255, 156, 25, 0) 0 0 10px 0;
          animation: shadow-blink 1s linear 3;
        }

        .blink-content {
          @keyframes reference-blink {
            50% {
              background-color: #ff9c19;
            }
          }

          animation: reference-blink 1s linear 3;
        }

        .message-label {
          align-self: flex-end;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #b6b8ba;
          word-break: keep-all;
          flex: 0 0 auto;
          margin: 0 8px;

          &.fail {
            width: 15px;
            height: 15px;
            border-radius: 15px;
            background: red;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
          }

          &.loading-circle {
            opacity: 0;
            animation: circle-loading 2s linear 1s infinite;
          }

          @keyframes circle-loading {
            0% {
              transform: rotate(0);
              opacity: 1;
            }

            100% {
              opacity: 1;
              transform: rotate(360deg);
            }
          }
        }

        .align-self-bottom {
          align-self: flex-end;
        }
      }

      .message-bubble-tool {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 15px;

        div {
          padding: 7px 6px;
          margin-right: 10px;
          border-radius: 5px;
          background: #dee5f6;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;

          img {
            width: 18px;
            height: 18px;
            display: inline-block;
          }

          span {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #000000;
            margin-left: 8px !important;
          }
        }
      }

      .reference-list {
        display: flex;
        flex-direction: column;
        margin-top: 10px;
        align-items: flex-end;

        .reference-image {
          // width: 276px;
          display: flex;
          flex-direction: row;
          // width: 90px;
          height: 56px;
          background: #dcdcdc;
          border-radius: 10px;
          margin-bottom: 10px;
          padding: 8px;

          .reference-image-title {
            height: 11px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 11px;
            color: #434343;
            line-height: 17px;
            margin-left: 11px;
            margin-right: 17px;
          }

          img {
            width: 42px;
            height: 42px;
            object-fit: cover;
            border-radius: 8px;
          }
        }

        .reference-text {
          margin-bottom: 10px;
          display: block;
          width: 143px;
          height: 34px;
          background: #dcdcdc;
          border-radius: 10px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 11px;
          color: #434343;
          line-height: 34px;
          padding: 0 7px;
          white-space: nowrap;
          /* 防止文本换行 */
          overflow: hidden;
          /* 隐藏溢出的内容 */
          text-overflow: ellipsis;
          /* 显示省略符号来代表被修剪的文本 */
        }

        .reference-video {
          display: flex;
          flex-direction: row;
          background: #dcdcdc;
          margin-bottom: 10px;
          border-radius: 10px;
          padding: 8px;
          height: 56px;

          .reference-video-title {
            // width: 40px;
            height: 11px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 11px;
            color: #434343;
            line-height: 11px;
            margin: 0 11px;
            // 超出省略号
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .img-box {
            position: relative;
            width: 42px;
            height: 42px;

            .cover {
              width: 42px;
              height: 42px;
              object-fit: cover;
              border-radius: 8px;
            }

            .play {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 18px;
              height: 18px;
            }
          }
        }

        .reference-audio {
          display: flex;
          flex-direction: row;
          background: #dcdcdc;
          margin-bottom: 10px;
          border-radius: 10px;
          padding: 8px;
          height: 56px;

          .reference-audio-title {
            // width: 40px;
            height: 11px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 11px;
            color: #434343;
            line-height: 11px;
            margin: 0 11px;
            // 超出省略号
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .img-box {
            position: relative;
            width: 42px;
            height: 42px;

            .cover {
              width: 42px;
              height: 42px;
              object-fit: cover;
              border-radius: 8px;
            }
          }
        }
      }

      .reference-list-more {
        width: 209px;
        height: 111px;
        background: #ffffff;
        border-radius: 10px;
        margin-top: 11px;

        p {
          margin-top: 12px !important;
          margin-left: 10px !important;
          margin-bottom: 10px !important;
          height: 12px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #434343;
          line-height: 12px;
          // 超出省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        div {
          height: 11px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 11px;
          color: #434343;
          line-height: 11px;
          margin-left: 10px;
          margin-bottom: 8px;
          // 超出省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .message-bubble-tips {
        display: flex;
        flex-direction: column;

        .message-bubble-tips-item {
          word-break: break-word;
          font-size: 14px;

          // margin-left: 10px;
          .message-bubble-tips-item-text {
            background: #dee5f6;
            border-radius: 10px;
            padding: 10px 16px;
            margin-bottom: 10px;
          }
        }
      }

      .message-bubble-tips-top {
        margin-top: 10px;
      }
    }
  }

  .reverse {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-start;
  }

  .message-bubble-extra-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
