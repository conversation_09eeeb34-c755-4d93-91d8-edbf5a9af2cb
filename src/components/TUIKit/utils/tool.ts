import axios from 'axios'
import OSS from 'ali-oss'
import { getAliyun2 } from '@/api/common'
import md5 from 'md5'

// 重新排序
export const reorder = <T>(list: T[], startIndex: number, endIndex: number): T[] => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

// 地址参数
export const getUrlParam = (name: string, url?: string) => {
  const u = url || window.location.href,
    reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'),
    r = u.substring(u.indexOf('?') + 1).match(reg)
  return r != null ? decodeURI(r[2]) : ''
}

// 下载文件
export const downloadFile = (blob: any, filename: string) => {
  const a = window.document.createElement('a')
  a.download = `${decodeURI(filename)}`
  a.href = window.URL.createObjectURL(blob)
  a.click()
  window.URL.revokeObjectURL(a.href)
}

// 防抖
export const debounce = (func: (...args: any[]) => void, time: number | undefined) => {
  let timer: ReturnType<typeof setTimeout> | undefined
  return (...args: any[]) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func(...args)
    }, time)
  }
}

// 节流
export const throttle = (callback: (...args: any[]) => void, wait = 3000) => {
  let timer: ReturnType<typeof setTimeout> | undefined
  let startTime: number
  return (...args: any[]) => {
    const now = +new Date()
    if (startTime && now < startTime + wait) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        startTime = now
        callback(...args)
      }, wait)
    } else {
      startTime = now
      callback(...args)
    }
  }
}

// 强行睡觉,默认800
export const sleep = (time: number = 800) => {
  return new Promise(resolve =>
    setTimeout(() => {
      resolve(true)
    }, time)
  )
}

// 生成uuid
export function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

// dataURL to file
export const dataURLToFile = (dataURL: string, filename: string) => {
  const arr = dataURL.split(',')
  const mime = arr[0].match(/:(.*?);/)![1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

// img url to base64
export const convertImgToBase64 = async (url: string): Promise<string> => {
  const response = await axios({ url, responseType: 'blob' })
  // 确保response.data是Blob类型
  const blob = response.data as unknown as Blob
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      resolve(reader.result as string)
    }
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

// get img width height
export const getImgInfo = async (url: string) => {
  const base64 = await convertImgToBase64(url)
  return new Promise(resolve => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
  })
}

// blob to base64
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise(resolve => {
    const reader = new FileReader()
    reader.onloadend = () => {
      resolve(reader.result as string)
    }
    reader.readAsDataURL(blob)
  })
}

// get base64 width height
export const getBase64Info = async (base64: string): Promise<{ width: number; height: number; img: HTMLImageElement }> => {
  return new Promise(resolve => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      resolve({ width: img.width, height: img.height, img })
    }
  })
}

// base64 mp3 to file
export const base64ToFile = (base64: string, filename: string) => {
  const arr = base64.split(',')
  const mime = arr[0].match(/:(.*?);/)![1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

// base64 img change size, Long side=512,Short side=auto
export const resizeBase64Img = async (base64: string, maxSide: number = 512, isCover = true): Promise<{ width: number; height: number; base64: string }> => {
  const info = await getBase64Info(base64)
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    throw new Error('Unable to get canvas context')
  }

  const ratio = info.width > info.height ? maxSide / info.width : maxSide / info.height

  if (!isCover && info.width < maxSide && info.height < maxSide) {
    canvas.width = info.width
    canvas.height = info.height
    ctx.drawImage(info.img, 0, 0, canvas.width, canvas.height)
    const base64Url = canvas.toDataURL()
    return { width: canvas.width, height: canvas.height, base64: base64Url }
  }
  canvas.width = info.width * ratio
  canvas.height = info.height * ratio

  ctx.drawImage(info.img, 0, 0, canvas.width, canvas.height)
  const base64Url = canvas.toDataURL()
  return { width: canvas.width, height: canvas.height, base64: base64Url }
}

// base64 img change size, Long side=512,Short side=auto
export const getDeviceType = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  // 使用更现代的方法检测iPad
  const isiPad = navigator.userAgent.match(/(iPad)/) || (/Macintosh/i.test(navigator.userAgent) && navigator.maxTouchPoints > 1)
  if (/tablet|ipad|playbook|silk|kindle/i.test(userAgent) || isiPad) {
    return 'tablet'
  } else if (/mobile|android|touch|webos|hpwos/i.test(userAgent)) {
    return 'mobile'
  } else {
    return 'desktop'
  }
}

export const deviceBrowser = () => {
  const ua = navigator.userAgent.toLocaleLowerCase()
  // Safari
  // chrome
  // firefox
  // opera
  // IE
  // Edge
  // QQBrowser
  // UC
  // WeChat
  // Android
  // iOS
  // unknown
  if (ua.indexOf('micromessenger') > -1) {
    return 'WeChat'
  }
  if (ua.indexOf('qqbrowser') > -1) {
    return 'QQBrowser'
  }
  if (ua.indexOf('ucbrowser') > -1) {
    return 'UC'
  }
  if (ua.indexOf('edge') > -1) {
    return 'Edge'
  }
  if (ua.indexOf('opr') > -1 || ua.indexOf('opera') > -1) {
    return 'opera'
  }
  if (ua.indexOf('chrome') > -1) {
    return 'chrome'
  }
  if (ua.indexOf('safari') > -1) {
    return 'Safari'
  }
  if (ua.indexOf('firefox') > -1) {
    return 'firefox'
  }
  if (ua.indexOf('trident') > -1 || ua.indexOf('msie') > -1) {
    return 'IE'
  }
  if (ua.indexOf('android') > -1) {
    return 'Android'
  }
  if (/iphone|ipad|ipod/.test(ua)) {
    return 'iOS'
  }
  return 'unknown'
}

if (!localStorage.uuid) {
  localStorage.uuid = getUUID()
}

export const headerInfo = {
  deviceId: localStorage.uuid || getUUID(),
  appVersion: import.meta.env.VITE_APP_TIME,
  deviceType: getDeviceType(),
  deviceBrowser: deviceBrowser(),
}

export const borwserEnv = {
  isWechat: /micromessenger/.test(navigator.userAgent.toLowerCase()),
  isAndroid: /android/.test(navigator.userAgent.toLowerCase()),
  isIOS: /iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()),
}

// hex to rgb
export const hexToRgb = (hex: string) => {
  const rgb = []
  for (let i = 1; i < 7; i += 2) {
    rgb.push(parseInt('0x' + hex.slice(i, i + 2)))
  }
  return rgb
}

// is1V1App
export const isApp = () => {
  const ua = navigator.userAgent.toLowerCase() //获取判断用的对象
  if (ua.indexOf('ydy') > -1 || ua.indexOf('vwb') > -1) {
    return true
  }
  return false
}

// 解析 query格式字符串为 object
export function parseQuery(query: string) {
  const queryWithoutStart = query.indexOf('?') === 0 ? query.substring(1) : query
  const uaFormated: Record<string, string> = {}
  const strs = queryWithoutStart.split('&')
  for (let _i = 0, strs_1 = strs; _i < strs_1.length; _i++) {
    const querySingle = strs_1[_i]
    uaFormated[querySingle.split('=')[0]] = decodeURIComponent(querySingle.split('=')[1])
  }
  return uaFormated
}
// 1v1 UA

// iOS老师
// const ua ='userid=4175182&channel=48&ischeck=0&buildversion=3081700&ua=ydy-ipad&bundleId=com.msb.ArtVideoWB.Teacher&cversion=3.8.17&token=Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI0MTc1MTgyIiwic3ViIjoiNDE3NTE4MiIsImlhdCI6MTc0NDg3NTUwNCwiYXVkIjoidXNlciIsImV4cCI6MTc1MzUxNTUwNH0.CCDvKAe0O5gkSkaQzdxJIjBMolzZ0LbArusQy4y-WnZHrsLFwdz6FOoY1NaUoOj8FeqIEH1hZfZwg8Kl_hluSg&identity=0&idfa=3efbe57643af31f4d73cbc34da8b80ab&oua=Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148'
//   {
//     "userid": "4175182",
//     "channel": "48",
//     "ischeck": "0",
//     "buildversion": "3081700",
//     "ua": "ydy-ipad",
//     "bundleId": "com.msb.ArtVideoWB.Teacher",
//     "cversion": "3.8.17",
//     "token": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI0MTc1MTgyIiwic3ViIjoiNDE3NTE4MiIsImlhdCI6MTc0NDg3NTUwNCwiYXVkIjoidXNlciIsImV4cCI6MTc1MzUxNTUwNH0.CCDvKAe0O5gkSkaQzdxJIjBMolzZ0LbArusQy4y-WnZHrsLFwdz6FOoY1NaUoOj8FeqIEH1hZfZwg8Kl_hluSg",
//     "identity": "0",
//     "idfa": "3efbe57643af31f4d73cbc34da8b80ab",
//     "oua": "Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
// }

// iOS学员
// const ua = 'appLanguage=zh-CN&identity=1&devicename=iPad13,4&isMscUser=0&bundleId=com.msb.ArtVideoWB&userid=4175037&buildversion=3081700&isVip=1&userType=1&idfa=C025D254-E256-4FF2-990C-782076FA3B7E&channel=48&token=Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI0MTc1MDM3Iiwic3ViIjoiNDE3NTAzNyIsImlhdCI6MTc0NDk2OTQyMywiYXVkIjoidXNlciIsImV4cCI6MTc1MzYwOTQyM30.xFl4PVdT1rRYYFhSd97O6lCOnFKnvlLDdNIWkW4a9fUddDixcC6NrIS7laVQHRfafFWxfbVe6JIjmhXqWx1D_g&cversion=3.8.17&deviceid=iPad13,4&ischeck=0&ua=ydy-ipad&oua=Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148'
// {
//     "appLanguage": "zh-CN",
//     "identity": "1",
//     "devicename": "iPad13,4",
//     "isMscUser": "0",
//     "bundleId": "com.msb.ArtVideoWB",
//     "userid": "4175037",
//     "buildversion": "3081700",
//     "isVip": "1",
//     "userType": "1",
//     "idfa": "C025D254-E256-4FF2-990C-782076FA3B7E",
//     "channel": "48",
//     "token": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI0MTc1MDM3Iiwic3ViIjoiNDE3NTAzNyIsImlhdCI6MTc0NDk2OTQyMywiYXVkIjoidXNlciIsImV4cCI6MTc1MzYwOTQyM30.xFl4PVdT1rRYYFhSd97O6lCOnFKnvlLDdNIWkW4a9fUddDixcC6NrIS7laVQHRfafFWxfbVe6JIjmhXqWx1D_g",
//     "cversion": "3.8.17",
//     "deviceid": "iPad13,4",
//     "ischeck": "0",
//     "ua": "ydy-ipad",
//     "oua": "Mozilla/5.0 (iPad; CPU OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148"
// }

// Android学员
// const ua = 'idfa=&userid=4157958&token=Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI0MTU3OTU4Iiwic3ViIjoiNDE1Nzk1OCIsImlhdCI6MTc0NDk2OTkyOSwiYXVkIjoidXNlciIsImV4cCI6MTc1MzYwOTkyOX0.XWMpIFYjSToVUhsYhgFxKNJa_agIcIyEjUzWsc0J6Xlil_znlGQ4YRiBkjXjVbBs8JFnx1i53fUxIQSexqnVbg&ua=vwb-android&buildversion=3815000&cversion=3.8.15&osVersionCode=29&oua=Mozilla/5.0 (Linux; Android 10; SCM-W09 Build/HUAWEISCM-W09; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.196 Safari/537.36&identity=1&appLanguage=zh-CN&userType=0&isMscUser=0&isVip=1'
// {
//     "idfa": "",
//     "userid": "4157958",
//     "token": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI0MTU3OTU4Iiwic3ViIjoiNDE1Nzk1OCIsImlhdCI6MTc0NDk2OTkyOSwiYXVkIjoidXNlciIsImV4cCI6MTc1MzYwOTkyOX0.XWMpIFYjSToVUhsYhgFxKNJa_agIcIyEjUzWsc0J6Xlil_znlGQ4YRiBkjXjVbBs8JFnx1i53fUxIQSexqnVbg",
//     "ua": "vwb-android",
//     "buildversion": "3815000",
//     "cversion": "3.8.15",
//     "osVersionCode": "29",
//     "oua": "Mozilla/5.0 (Linux; Android 10; SCM-W09 Build/HUAWEISCM-W09; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.196 Safari/537.36",
//     "identity": "1",
//     "appLanguage": "zh-CN",
//     "userType": "0",
//     "isMscUser": "0",
//     "isVip": "1"
// }

export const getVideoBase64 = (source: string | File, currentTime = 1, timeout = 10000): Promise<{ width: number; height: number; base64: string }> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.setAttribute('crossOrigin', 'anonymous')
    video.setAttribute('preload', 'auto')

    // 定义清理函数
    let cleanup = () => {
      clearTimeout(timeoutId)
      video.removeEventListener('error', handleError)
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('canplay', handleCanPlay)
      video.removeEventListener('seeked', handleSeeked)
      video.pause()
      video.src = ''
    }

    // 根据输入类型设置视频源
    if (source instanceof File) {
      // 如果是File对象，创建URL
      const objectUrl = URL.createObjectURL(source)
      video.setAttribute('src', objectUrl)

      // 扩展清理函数以释放URL
      const originalCleanup = cleanup
      cleanup = () => {
        URL.revokeObjectURL(objectUrl)
        originalCleanup()
      }
    } else {
      // 如果是URL字符串
      video.setAttribute('src', source)
    }

    // 设置超时处理
    const timeoutId = setTimeout(() => {
      cleanup()
      // 超时后尝试使用阿里云OSS的视频处理功能（仅当source是URL时）
      if (typeof source === 'string') {
        fallbackToAliyunOSS(source, resolve, reject)
      } else {
        reject(new Error('获取视频封面超时'))
      }
    }, timeout)

    const handleError = (e: any) => {
      console.log('视频加载错误', e)
      cleanup()
      // 错误后尝试使用阿里云OSS的视频处理功能（仅当source是URL时）
      if (typeof source === 'string') {
        fallbackToAliyunOSS(source, resolve, reject)
      } else {
        reject(new Error('加载视频失败'))
      }
    }

    // 处理视频元数据加载完成
    const handleLoadedMetadata = () => {
      console.log('loadedmetadata')
      // 如果视频时长大于0，直接尝试获取封面
      if (video.duration > 0) {
        // 设置视频时间点
        video.currentTime = Math.min(currentTime, video.duration || 1)
      }
    }

    // 处理视频数据加载完成
    const handleLoadedData = () => {
      console.log('loadeddata')
      // 设置视频时间点
      video.currentTime = Math.min(currentTime, video.duration || 1)
    }

    // 处理视频可以播放
    const handleCanPlay = () => {
      console.log('canplay')
      // 设置视频时间点
      video.currentTime = Math.min(currentTime, video.duration || 1)
    }

    // 处理视频跳转完成
    const handleSeeked = () => {
      console.log('seeked')
      captureFrame()
    }

    // 捕获视频帧
    const captureFrame = () => {
      try {
        const canvas = document.createElement('canvas')
        const width = video.videoWidth || 300
        const height = video.videoHeight || 200
        canvas.width = width
        canvas.height = height
        const ctx = canvas.getContext('2d')
        if (ctx) {
          ctx.drawImage(video, 0, 0, width, height)
          const dataURL = canvas.toDataURL('image/jpeg')
          cleanup()
          resolve({
            width,
            height,
            base64: dataURL,
          })
        } else {
          cleanup()
          // 无法获取画布上下文时尝试使用阿里云OSS的视频处理功能（仅当source是URL时）
          if (typeof source === 'string') {
            fallbackToAliyunOSS(source, resolve, reject)
          } else {
            reject(new Error('无法获取画布上下文'))
          }
        }
      } catch (error) {
        cleanup()
        // 捕获帧出错时尝试使用阿里云OSS的视频处理功能（仅当source是URL时）
        if (typeof source === 'string') {
          fallbackToAliyunOSS(source, resolve, reject)
        } else {
          reject(error)
        }
      }
    }

    // 添加多个事件监听，提高成功率
    video.addEventListener('error', handleError)
    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('canplay', handleCanPlay)
    video.addEventListener('seeked', handleSeeked)

    // 开始加载视频
    video.load()
  })
}

// 使用阿里云OSS的视频处理功能获取封面
const fallbackToAliyunOSS = async (url: string, resolve: (value: { width: number; height: number; base64: string }) => void, reject: (reason?: any) => void) => {
  try {
    console.log('使用阿里云OSS视频处理功能获取封面')

    // 构建阿里云OSS视频处理URL
    const snapshotUrl = `${url}?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast`

    // 获取图片信息
    const imgInfo = (await getImgInfo(snapshotUrl)) as { width: number; height: number }

    // 获取图片base64
    const base64 = await convertImgToBase64(snapshotUrl)

    resolve({
      width: imgInfo.width,
      height: imgInfo.height,
      base64,
    })
  } catch (error) {
    console.error('阿里云OSS视频处理获取封面失败', error)
    reject(error)
  }
}

// OSS客户端类
class OssClient {
  private static instance: OssClient | null = null
  private client: OSS | null = null
  private preUrl: string = ''
  private cdnUrl: string = ''
  private initialized: boolean = false

  private constructor() {}

  public static getInstance(): OssClient {
    if (!OssClient.instance) {
      OssClient.instance = new OssClient()
    }
    return OssClient.instance
  }

  // 获取OSS配置信息
  private async getOssPolicy(): Promise<any> {
    const res = await getAliyun2()
    return res.data
  }

  // 初始化OSS客户端
  private async initClient(): Promise<void> {
    if (this.initialized) return

    const policy = await this.getOssPolicy()
    this.preUrl = policy?.preUrl
    this.cdnUrl = policy?.cdnUrl

    this.client = new OSS({
      region: 'oss-cn-hangzhou',
      accessKeyId: policy?.credentials?.accessKeyId,
      accessKeySecret: policy?.credentials?.accessKeySecret,
      stsToken: policy?.credentials?.securityToken,
      bucket: policy?.bucketName,
      refreshSTSToken: async () => {
        // 调用后端接口获取新的STS Token
        const res = await this.getOssPolicy()
        return {
          accessKeyId: res?.credentials?.accessKeyId,
          accessKeySecret: res?.credentials?.accessKeySecret,
          stsToken: res?.credentials?.securityToken,
        }
      },
      refreshSTSTokenInterval: 10 * 60 * 1000, // 10分钟刷新一次
    })

    this.initialized = true
  }

  // 上传文件
  public async upload(file: File | Blob, fileName?: string): Promise<string> {
    if (!this.initialized) {
      await this.initClient()
    }

    if (!this.client) {
      throw new Error('OSS client not initialized')
    }

    let fixName = 'file'
    if (file instanceof File) {
      const suffix = file.name.split('.').pop()
      const md5Name = md5(file.name)
      fixName = `${md5Name}.${suffix}`
    }

    // 生成OSS文件路径
    const finalFileName = fileName || `${this.preUrl}/${new Date().getTime()}-${fixName}`

    // 执行上传
    const result = await this.client.put(finalFileName, file)

    // 返回CDN URL
    return `${this.cdnUrl}/${result.name}`
  }

  // 分片上传文件（支持大文件和进度回调）
  public async multipartUpload(
    file: File | Blob,
    fileName?: string,
    options?: {
      partSize?: number // 分片大小，默认1MB
      parallel?: number // 并发数，默认3
      onProgress?: (progress: number) => void // 进度回调
    }
  ): Promise<string> {
    if (!this.initialized) {
      await this.initClient()
    }

    if (!this.client) {
      throw new Error('OSS client not initialized')
    }

    let fixName = 'file'
    if (file instanceof File) {
      const suffix = file.name.split('.').pop()
      const md5Name = md5(file.name)
      fixName = `${md5Name}.${suffix}`
    }

    // 生成OSS文件路径
    const finalFileName = fileName || `${this.preUrl}/${new Date().getTime()}-${fixName}`

    // 默认配置
    const config = {
      partSize: options?.partSize || 1024 * 1024, // 1MB
      parallel: options?.parallel || 3,
      progress: options?.onProgress || (() => {}),
    }

    // 执行分片上传
    const result = await this.client.multipartUpload(finalFileName, file, {
      partSize: config.partSize,
      parallel: config.parallel,
      progress: (p: number) => {
        // p 是 0-1 之间的小数，转换为 0-100 的百分比
        config.progress(Math.round(p * 100))
      },
    })

    // 返回CDN URL
    return `${this.cdnUrl}/${result.name}`
  }

  // 获取预签名URL
  public getPreUrl(): string {
    return this.preUrl
  }

  // 获取CDN URL
  public getCdnUrl(): string {
    return this.cdnUrl
  }
}

// 导出OSS客户端实例获取函数
export const ossClient = (): OssClient => {
  return OssClient.getInstance()
}
