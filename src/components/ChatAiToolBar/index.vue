<template>
  <!-- 使用 Transition 组件包装弹窗，添加遮罩层动画 -->
  <Transition name="modal" appear>
    <div class="ai-tool" @click="close">
      <!-- 使用 Transition 组件为内容添加滑入动画 -->
      <Transition name="slide-up" appear>
        <div class="content" @click.stop>
          <div class="head">
            <div class="left"></div>
            <div class="title">AI功能</div>
            <div class="close" @click="close">
              <img :src="closeImg" alt="" />
            </div>
          </div>
          <div class="tool-box" v-for="item in chatStore.tools" :key="item?.functionClassify">
            <div class="title">{{ item?.functionClassify }}</div>
            <div class="tools">
              <div @click="chooseTool(tool.functionName)" class="tool" v-for="tool in item?.toolList"
                :key="tool.functionName">
                <img :src="tool.icon" alt="" />
                <div class="item-text">{{ tool.functionName.replace('/', '') }}</div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script lang="ts" setup>
import useChatStore from '../../store/modules/chat'
import { onMounted, Transition } from 'vue'
import closeImg from '@/assets/images/ChatAiTool/close.png'
import { getToolsAll } from '@/api/aiChat'
import { LocationQueryValue, useRoute } from 'vue-router'
const chatStore = useChatStore()
const route = useRoute()
const { query } = route
const { courseid } = query
const emit = defineEmits(['chooseTool'])

defineOptions({
  name: 'ChatAiTool',
})


// 判断key可能是数组或者字符串或者null，取数组的第一项或者字符串或者null
const getQueryValue = (key: LocationQueryValue | LocationQueryValue[]) => {
  if (Array.isArray(key)) {
    return key[0]
  }
  return key
}
const chooseTool = (tool: string) => {
  emit('chooseTool', tool)
  close()
}

const close = () => {
  chatStore.setShowAiToolBar(false)
}

onMounted(() => {
  getToolsAll({
    courseId: getQueryValue(courseid),
  }).then((res: any) => {
    if (import.meta.env.DEV) {
      console.log('res', res)
    }
    chatStore.setTools(res.data)
  })
})
</script>

<style lang="scss" scoped>
// 遮罩层动画
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

// 弹窗滑动动画 - 从下到上滑入
.slide-up-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.ai-tool {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background: #00000040;

  .content {
    width: 100%;
    background: #dee5f6;
    border-radius: 15px 15px 0px 0px;
    padding: 0 25px 16px 25px;

    .head {
      height: 72px;
      display: flex;
      align-items: center;
      padding-bottom: 23px;

      .left {
        width: 40px;
        height: 40px;
      }

      .title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #000000;
        flex: 1;
        text-align: center;
      }

      .close {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 30px;
          height: 30px;
        }
      }
    }

    .tool-box {
      display: flex;
      flex-direction: column;
      margin-bottom: 19px;

      .title {
        height: 16px;
        font-family: HYZhengYuan;
        font-weight: normal;
        font-size: 16px;
        color: #000000;
        line-height: 16px;
        margin-bottom: 19px;
      }

      .tools {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(115px, 1fr));
        row-gap: 11px;
        column-gap: 6px;
        align-items: start;

        .tool {
          display: flex;
          flex-direction: row;
          align-items: center;
          background: #ffffff;
          border-radius: 5px;
          cursor: pointer;
          transition: all 0.2s ease;
          height: 35px;
          padding: 0 8px;

          img {
            width: 23px;
            height: 23px;
            object-fit: contain;
          }

          .item-text {
            flex: 1;
            text-align: center;
            line-height: 14px;
            word-break: break-all;
            overflow: hidden;
            max-width: 100%;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #000000;

          }
        }
      }
    }
  }

}
</style>
